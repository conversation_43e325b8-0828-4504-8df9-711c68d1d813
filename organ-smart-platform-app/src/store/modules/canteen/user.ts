/** 智慧食堂用户信息接口 */
export interface ICanteenUserInfo {
  /** 用户ID */
  id?: string | number;
  /** 用户名 */
  username?: string;
  /** 真实姓名 */
  realName?: string;
  /** 邮箱 */
  email?: string;
  /** 手机号 */
  phone?: string;
  /** 头像 */
  avatar?: string;
  /** 工号 */
  employeeNo?: string;
  /** 部门ID */
  deptId?: string | number;
  /** 部门名称 */
  deptName?: string;
  /** 用户状态 */
  status?: number;
  /** 余额 */
  balance?: number;
  /** 消费总额 */
  totalConsumption?: number;
  /** 会员等级 */
  memberLevel?: string;
  /** 创建时间 */
  createTime?: string;
  /** 最后登录时间 */
  lastLoginTime?: string;
  /** 最后消费时间 */
  lastConsumptionTime?: string;
}

/** 智慧食堂用户store类型 */
export interface ICanteenUserStore {
  /** 用户登录token */
  token: string;
  /** 用户信息 */
  userInfo: ICanteenUserInfo;
  /** 用户刷新token */
  refreshToken: string;
  /** 用户权限 */
  permissions: string[];
  /** 用户角色 */
  roles: string[];
  /** 用户 accessToken */
  accessToken: string;
  /** 是否已登录 */
  isLoggedIn: boolean;
  /** 登录时间戳 */
  loginTime: number;
  /** token过期时间戳 */
  tokenExpireTime: number;
  /** 当前选择的食堂ID */
  currentCanteenId: string | number;
  /** 购物车信息 */
  cart: {
    items: any[];
    totalAmount: number;
    totalCount: number;
  };
}

/**
 * 智慧食堂用户store模块
 */
export const canteenUserModule = {
  namespaced: true,
  state: (): ICanteenUserStore => ({
    /** 用户登录token */
    token: "",
    /** 用户信息 */
    userInfo: {},
    /** 用户刷新token */
    refreshToken: "",
    /** 用户权限 */
    permissions: [],
    /** 用户角色 */
    roles: [],
    /** 用户 accessToken */
    accessToken: "",
    /** 是否已登录 */
    isLoggedIn: false,
    /** 登录时间戳 */
    loginTime: 0,
    /** token过期时间戳 */
    tokenExpireTime: 0,
    /** 当前选择的食堂ID */
    currentCanteenId: "",
    /** 购物车信息 */
    cart: {
      items: [],
      totalAmount: 0,
      totalCount: 0,
    },
  }),

  getters: {
    /**
     * 获取用户是否已登录
     * @param state 状态
     * @returns 是否已登录
     */
    isAuthenticated: (state: ICanteenUserStore): boolean => {
      return !!(
        state.token &&
        state.isLoggedIn &&
        Date.now() < state.tokenExpireTime
      );
    },

    /**
     * 获取用户显示名称
     * @param state 状态
     * @returns 用户显示名称
     */
    userDisplayName: (state: ICanteenUserStore): string => {
      return state.userInfo.realName || state.userInfo.username || "未知用户";
    },

    /**
     * 获取用户余额
     * @param state 状态
     * @returns 用户余额
     */
    userBalance: (state: ICanteenUserStore): number => {
      return state.userInfo.balance || 0;
    },

    /**
     * 检查用户是否有指定权限
     * @param state 状态
     * @returns 权限检查函数
     */
    hasPermission:
      (state: ICanteenUserStore) =>
      (permission: string): boolean => {
        return state.permissions.includes(permission);
      },

    /**
     * 检查用户是否有指定角色
     * @param state 状态
     * @returns 角色检查函数
     */
    hasRole:
      (state: ICanteenUserStore) =>
      (role: string): boolean => {
        return state.roles.includes(role);
      },

    /**
     * 获取购物车商品数量
     * @param state 状态
     * @returns 购物车商品数量
     */
    cartItemCount: (state: ICanteenUserStore): number => {
      return state.cart.totalCount;
    },

    /**
     * 获取购物车总金额
     * @param state 状态
     * @returns 购物车总金额
     */
    cartTotalAmount: (state: ICanteenUserStore): number => {
      return state.cart.totalAmount;
    },
  },

  mutations: {
    /**
     * 设置用户token
     * @param state 状态
     * @param token token值
     */
    SET_TOKEN(state: ICanteenUserStore, token: string) {
      state.token = token;
    },

    /**
     * 设置用户信息
     * @param state 状态
     * @param userInfo 用户信息
     */
    SET_USER_INFO(state: ICanteenUserStore, userInfo: ICanteenUserInfo) {
      state.userInfo = userInfo;
    },

    /**
     * 设置刷新token
     * @param state 状态
     * @param refreshToken 刷新token
     */
    SET_REFRESH_TOKEN(state: ICanteenUserStore, refreshToken: string) {
      state.refreshToken = refreshToken;
    },

    /**
     * 设置用户权限
     * @param state 状态
     * @param permissions 权限列表
     */
    SET_PERMISSIONS(state: ICanteenUserStore, permissions: string[]) {
      state.permissions = permissions;
    },

    /**
     * 设置用户角色
     * @param state 状态
     * @param roles 角色列表
     */
    SET_ROLES(state: ICanteenUserStore, roles: string[]) {
      state.roles = roles;
    },

    /**
     * 设置访问token
     * @param state 状态
     * @param accessToken 访问token
     */
    SET_ACCESS_TOKEN(state: ICanteenUserStore, accessToken: string) {
      state.accessToken = accessToken;
    },

    /**
     * 设置登录状态
     * @param state 状态
     * @param isLoggedIn 是否已登录
     */
    SET_LOGIN_STATUS(state: ICanteenUserStore, isLoggedIn: boolean) {
      state.isLoggedIn = isLoggedIn;
    },

    /**
     * 设置登录时间
     * @param state 状态
     * @param loginTime 登录时间戳
     */
    SET_LOGIN_TIME(state: ICanteenUserStore, loginTime: number) {
      state.loginTime = loginTime;
    },

    /**
     * 设置token过期时间
     * @param state 状态
     * @param expireTime 过期时间戳
     */
    SET_TOKEN_EXPIRE_TIME(state: ICanteenUserStore, expireTime: number) {
      state.tokenExpireTime = expireTime;
    },

    /**
     * 设置当前食堂ID
     * @param state 状态
     * @param canteenId 食堂ID
     */
    SET_CURRENT_CANTEEN_ID(
      state: ICanteenUserStore,
      canteenId: string | number
    ) {
      state.currentCanteenId = canteenId;
    },

    /**
     * 设置购物车
     * @param state 状态
     * @param cart 购物车信息
     */
    SET_CART(state: ICanteenUserStore, cart: any) {
      state.cart = cart;
    },

    /**
     * 添加商品到购物车
     * @param state 状态
     * @param item 商品信息
     */
    ADD_TO_CART(state: ICanteenUserStore, item: any) {
      const existingItem = state.cart.items.find(
        (cartItem) => cartItem.id === item.id
      );
      if (existingItem) {
        existingItem.quantity += item.quantity || 1;
      } else {
        state.cart.items.push({
          ...item,
          quantity: item.quantity || 1,
        });
      }
      // 重新计算总数和总金额
      state.cart.totalCount = state.cart.items.reduce(
        (total, item) => total + item.quantity,
        0
      );
      state.cart.totalAmount = state.cart.items.reduce(
        (total, item) => total + item.price * item.quantity,
        0
      );
    },

    /**
     * 从购物车移除商品
     * @param state 状态
     * @param itemId 商品ID
     */
    REMOVE_FROM_CART(state: ICanteenUserStore, itemId: string | number) {
      state.cart.items = state.cart.items.filter((item) => item.id !== itemId);
      // 重新计算总数和总金额
      state.cart.totalCount = state.cart.items.reduce(
        (total, item) => total + item.quantity,
        0
      );
      state.cart.totalAmount = state.cart.items.reduce(
        (total, item) => total + item.price * item.quantity,
        0
      );
    },

    /**
     * 清空购物车
     * @param state 状态
     */
    CLEAR_CART(state: ICanteenUserStore) {
      state.cart = {
        items: [],
        totalAmount: 0,
        totalCount: 0,
      };
    },

    /**
     * 更新用户余额
     * @param state 状态
     * @param balance 新余额
     */
    UPDATE_BALANCE(state: ICanteenUserStore, balance: number) {
      if (state.userInfo) {
        state.userInfo.balance = balance;
      }
    },

    /**
     * 清除所有认证信息
     * @param state 状态
     */
    CLEAR_AUTH(state: ICanteenUserStore) {
      state.token = "";
      state.userInfo = {};
      state.refreshToken = "";
      state.permissions = [];
      state.roles = [];
      state.accessToken = "";
      state.isLoggedIn = false;
      state.loginTime = 0;
      state.tokenExpireTime = 0;
      state.currentCanteenId = "";
      state.cart = {
        items: [],
        totalAmount: 0,
        totalCount: 0,
      };
    },
  },

  actions: {
    /**
     * 用户登录
     * @param context Vuex上下文
     * @param loginData 登录数据
     */
    async login(
      { commit }: any,
      loginData: {
        token: string;
        accessToken?: string;
        refreshToken?: string;
        userInfo?: ICanteenUserInfo;
        permissions?: string[];
        roles?: string[];
        expiresIn?: number; // token有效期（秒）
      }
    ) {
      try {
        const now = Date.now();
        const expireTime = loginData.expiresIn
          ? now + loginData.expiresIn * 1000
          : now + 24 * 60 * 60 * 1000; // 默认24小时

        commit("SET_TOKEN", loginData.token);
        commit("SET_ACCESS_TOKEN", loginData.accessToken || loginData.token);
        commit("SET_REFRESH_TOKEN", loginData.refreshToken || "");
        commit("SET_USER_INFO", loginData.userInfo || {});
        commit("SET_PERMISSIONS", loginData.permissions || []);
        commit("SET_ROLES", loginData.roles || []);
        commit("SET_LOGIN_STATUS", true);
        commit("SET_LOGIN_TIME", now);
        commit("SET_TOKEN_EXPIRE_TIME", expireTime);

        return Promise.resolve();
      } catch (error) {
        console.error("食堂用户登录失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 用户登出
     * @param context Vuex上下文
     */
    async logout({ commit }: any) {
      try {
        commit("CLEAR_AUTH");
        return Promise.resolve();
      } catch (error) {
        console.error("食堂用户登出失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 刷新token
     * @param context Vuex上下文
     * @param newTokenData 新的token数据
     */
    async refreshToken(
      { commit }: any,
      newTokenData: {
        token: string;
        accessToken?: string;
        expiresIn?: number;
      }
    ) {
      try {
        const now = Date.now();
        const expireTime = newTokenData.expiresIn
          ? now + newTokenData.expiresIn * 1000
          : now + 24 * 60 * 60 * 1000;

        commit("SET_TOKEN", newTokenData.token);
        commit(
          "SET_ACCESS_TOKEN",
          newTokenData.accessToken || newTokenData.token
        );
        commit("SET_TOKEN_EXPIRE_TIME", expireTime);

        return Promise.resolve();
      } catch (error) {
        console.error("食堂用户刷新token失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 更新用户信息
     * @param context Vuex上下文
     * @param userInfo 用户信息
     */
    async updateUserInfo({ commit }: any, userInfo: ICanteenUserInfo) {
      try {
        commit("SET_USER_INFO", userInfo);
        return Promise.resolve();
      } catch (error) {
        console.error("更新食堂用户信息失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 选择食堂
     * @param context Vuex上下文
     * @param canteenId 食堂ID
     */
    async selectCanteen({ commit }: any, canteenId: string | number) {
      try {
        commit("SET_CURRENT_CANTEEN_ID", canteenId);
        // 切换食堂时清空购物车
        commit("CLEAR_CART");
        return Promise.resolve();
      } catch (error) {
        console.error("选择食堂失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 添加商品到购物车
     * @param context Vuex上下文
     * @param item 商品信息
     */
    async addToCart({ commit }: any, item: any) {
      try {
        commit("ADD_TO_CART", item);
        return Promise.resolve();
      } catch (error) {
        console.error("添加到购物车失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 从购物车移除商品
     * @param context Vuex上下文
     * @param itemId 商品ID
     */
    async removeFromCart({ commit }: any, itemId: string | number) {
      try {
        commit("REMOVE_FROM_CART", itemId);
        return Promise.resolve();
      } catch (error) {
        console.error("从购物车移除失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 清空购物车
     * @param context Vuex上下文
     */
    async clearCart({ commit }: any) {
      try {
        commit("CLEAR_CART");
        return Promise.resolve();
      } catch (error) {
        console.error("清空购物车失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 更新用户余额
     * @param context Vuex上下文
     * @param balance 新余额
     */
    async updateBalance({ commit }: any, balance: number) {
      try {
        commit("UPDATE_BALANCE", balance);
        return Promise.resolve();
      } catch (error) {
        console.error("更新余额失败:", error);
        return Promise.reject(error);
      }
    },
  },
};

// 保持向后兼容
export default canteenUserModule;
