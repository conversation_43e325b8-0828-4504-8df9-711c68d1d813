/** 用户信息接口 */
export interface IUserInfo {
  /** 用户ID */
  id?: string | number;
  /** 用户名 */
  username?: string;
  /** 真实姓名 */
  realName?: string;
  /** 邮箱 */
  email?: string;
  /** 手机号 */
  phone?: string;
  /** 头像 */
  avatar?: string;
  /** 部门ID */
  deptId?: string | number;
  /** 部门名称 */
  deptName?: string;
  /** 用户状态 */
  status?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后登录时间 */
  lastLoginTime?: string;
}

/** 权限管理store类型 */
export interface IAuthStore {
  /** 用户登录token */
  token: string;
  /** 用户信息 */
  userInfo: IUserInfo;
  /** 用户刷新token */
  refreshToken: string;
  /** 用户权限 */
  permissions: string[];
  /** 用户角色 */
  roles: string[];
  /** 用户 accessToken */
  accessToken: string;
  /** 是否已登录 */
  isLoggedIn: boolean;
  /** 登录时间戳 */
  loginTime: number;
  /** token过期时间戳 */
  tokenExpireTime: number;
}

/**
 * 权限管理store模块
 */
export const authModule = {
  namespaced: true,
  state: (): IAuthStore => ({
    /** 用户登录token */
    token: "",
    /** 用户信息 */
    userInfo: {},
    /** 用户刷新token */
    refreshToken: "",
    /** 用户权限 */
    permissions: [],
    /** 用户角色 */
    roles: [],
    /** 用户 accessToken */
    accessToken: "",
    /** 是否已登录 */
    isLoggedIn: false,
    /** 登录时间戳 */
    loginTime: 0,
    /** token过期时间戳 */
    tokenExpireTime: 0,
  }),

  getters: {
    /**
     * 获取用户是否已登录
     * @param state 状态
     * @returns 是否已登录
     */
    isAuthenticated: (state: IAuthStore): boolean => {
      return !!(
        state.token &&
        state.isLoggedIn &&
        Date.now() < state.tokenExpireTime
      );
    },

    /**
     * 获取用户显示名称
     * @param state 状态
     * @returns 用户显示名称
     */
    userDisplayName: (state: IAuthStore): string => {
      return state.userInfo.realName || state.userInfo.username || "未知用户";
    },

    /**
     * 检查用户是否有指定权限
     * @param state 状态
     * @returns 权限检查函数
     */
    hasPermission:
      (state: IAuthStore) =>
      (permission: string): boolean => {
        return state.permissions.includes(permission);
      },

    /**
     * 检查用户是否有指定角色
     * @param state 状态
     * @returns 角色检查函数
     */
    hasRole:
      (state: IAuthStore) =>
      (role: string): boolean => {
        return state.roles.includes(role);
      },
  },

  mutations: {
    /**
     * 设置用户token
     * @param state 状态
     * @param token token值
     */
    SET_TOKEN(state: IAuthStore, token: string) {
      state.token = token;
    },

    /**
     * 设置用户信息
     * @param state 状态
     * @param userInfo 用户信息
     */
    SET_USER_INFO(state: IAuthStore, userInfo: IUserInfo) {
      state.userInfo = userInfo;
    },

    /**
     * 设置刷新token
     * @param state 状态
     * @param refreshToken 刷新token
     */
    SET_REFRESH_TOKEN(state: IAuthStore, refreshToken: string) {
      state.refreshToken = refreshToken;
    },

    /**
     * 设置用户权限
     * @param state 状态
     * @param permissions 权限列表
     */
    SET_PERMISSIONS(state: IAuthStore, permissions: string[]) {
      state.permissions = permissions;
    },

    /**
     * 设置用户角色
     * @param state 状态
     * @param roles 角色列表
     */
    SET_ROLES(state: IAuthStore, roles: string[]) {
      state.roles = roles;
    },

    /**
     * 设置访问token
     * @param state 状态
     * @param accessToken 访问token
     */
    SET_ACCESS_TOKEN(state: IAuthStore, accessToken: string) {
      state.accessToken = accessToken;
    },

    /**
     * 设置登录状态
     * @param state 状态
     * @param isLoggedIn 是否已登录
     */
    SET_LOGIN_STATUS(state: IAuthStore, isLoggedIn: boolean) {
      state.isLoggedIn = isLoggedIn;
    },

    /**
     * 设置登录时间
     * @param state 状态
     * @param loginTime 登录时间戳
     */
    SET_LOGIN_TIME(state: IAuthStore, loginTime: number) {
      state.loginTime = loginTime;
    },

    /**
     * 设置token过期时间
     * @param state 状态
     * @param expireTime 过期时间戳
     */
    SET_TOKEN_EXPIRE_TIME(state: IAuthStore, expireTime: number) {
      state.tokenExpireTime = expireTime;
    },

    /**
     * 清除所有认证信息
     * @param state 状态
     */
    CLEAR_AUTH(state: IAuthStore) {
      state.token = "";
      state.userInfo = {};
      state.refreshToken = "";
      state.permissions = [];
      state.roles = [];
      state.accessToken = "";
      state.isLoggedIn = false;
      state.loginTime = 0;
      state.tokenExpireTime = 0;
    },
  },

  actions: {
    /**
     * 用户登录
     * @param context Vuex上下文
     * @param loginData 登录数据
     */
    async login(
      { commit }: any,
      loginData: {
        token: string;
        accessToken?: string;
        refreshToken?: string;
        userInfo?: IUserInfo;
        permissions?: string[];
        roles?: string[];
        expiresIn?: number; // token有效期（秒）
      }
    ) {
      try {
        const now = Date.now();
        const expireTime = loginData.expiresIn
          ? now + loginData.expiresIn * 1000
          : now + 24 * 60 * 60 * 1000; // 默认24小时

        commit("SET_TOKEN", loginData.token);
        commit("SET_ACCESS_TOKEN", loginData.accessToken || loginData.token);
        commit("SET_REFRESH_TOKEN", loginData.refreshToken || "");
        commit("SET_USER_INFO", loginData.userInfo || {});
        commit("SET_PERMISSIONS", loginData.permissions || []);
        commit("SET_ROLES", loginData.roles || []);
        commit("SET_LOGIN_STATUS", true);
        commit("SET_LOGIN_TIME", now);
        commit("SET_TOKEN_EXPIRE_TIME", expireTime);

        return Promise.resolve();
      } catch (error) {
        console.error("登录失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 用户登出
     * @param context Vuex上下文
     */
    async logout({ commit }: any) {
      try {
        commit("CLEAR_AUTH");
        return Promise.resolve();
      } catch (error) {
        console.error("登出失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 刷新token
     * @param context Vuex上下文
     * @param newTokenData 新的token数据
     */
    async refreshToken(
      { commit }: any,
      newTokenData: {
        token: string;
        accessToken?: string;
        expiresIn?: number;
      }
    ) {
      try {
        const now = Date.now();
        const expireTime = newTokenData.expiresIn
          ? now + newTokenData.expiresIn * 1000
          : now + 24 * 60 * 60 * 1000;

        commit("SET_TOKEN", newTokenData.token);
        commit(
          "SET_ACCESS_TOKEN",
          newTokenData.accessToken || newTokenData.token
        );
        commit("SET_TOKEN_EXPIRE_TIME", expireTime);

        return Promise.resolve();
      } catch (error) {
        console.error("刷新token失败:", error);
        return Promise.reject(error);
      }
    },

    /**
     * 更新用户信息
     * @param context Vuex上下文
     * @param userInfo 用户信息
     */
    async updateUserInfo({ commit }: any, userInfo: IUserInfo) {
      try {
        commit("SET_USER_INFO", userInfo);
        return Promise.resolve();
      } catch (error) {
        console.error("更新用户信息失败:", error);
        return Promise.reject(error);
      }
    },
  },
};

// 保持向后兼容
export default authModule;
