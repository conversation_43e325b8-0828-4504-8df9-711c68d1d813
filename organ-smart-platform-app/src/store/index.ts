import Vue from "vue";
import Vuex from "vuex";
import { authModule } from "./modules/auth";
import { canteenUserModule } from "./modules/canteen/user";
import {
  authPersistedState,
  canteenUserPersistedState,
  generalPersistedState,
} from "./plugins/persistedstate";

Vue.use(Vuex);

/**
 * Vuex Store 配置
 * 包含auth和canteenUser模块，以及对应的持久化配置
 */
export default new Vuex.Store({
  modules: {
    /** 权限管理模块 */
    auth: authModule,
    /** 智慧食堂用户模块 */
    canteenUser: canteenUserModule,
  },
  plugins: [
    /** auth模块持久化插件（加密存储） */
    authPersistedState,
    /** canteenUser模块持久化插件（加密存储） */
    canteenUserPersistedState,
    /** 通用持久化插件（非敏感数据） */
    generalPersistedState,
  ],
  strict: process.env.NODE_ENV !== "production",
});
