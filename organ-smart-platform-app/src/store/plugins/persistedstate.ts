import createPersistedState from 'vuex-persistedstate'
import CryptoUtil from '@/utils/crypto'

/**
 * 敏感数据字段列表
 * 这些字段在存储时会被加密
 */
const SENSITIVE_FIELDS = [
  'token',
  'accessToken', 
  'refreshToken',
  'userInfo.password',
  'userInfo.phone',
  'userInfo.email',
  'userInfo.idCard'
]

/**
 * 检查字段路径是否为敏感字段
 * @param path 字段路径，如 'auth.token' 或 'canteenUser.userInfo.phone'
 * @returns 是否为敏感字段
 */
function isSensitiveField(path: string): boolean {
  return SENSITIVE_FIELDS.some(field => path.includes(field))
}

/**
 * 递归加密对象中的敏感字段
 * @param obj 需要处理的对象
 * @param prefix 字段路径前缀
 * @returns 处理后的对象
 */
function encryptSensitiveFields(obj: any, prefix: string = ''): any {
  if (obj === null || obj === undefined) return obj
  
  if (typeof obj !== 'object') {
    return isSensitiveField(prefix) ? CryptoUtil.encrypt(String(obj)) : obj
  }
  
  if (Array.isArray(obj)) {
    return obj.map((item, index) => 
      encryptSensitiveFields(item, `${prefix}[${index}]`)
    )
  }
  
  const result: any = {}
  for (const [key, value] of Object.entries(obj)) {
    const currentPath = prefix ? `${prefix}.${key}` : key
    result[key] = encryptSensitiveFields(value, currentPath)
  }
  
  return result
}

/**
 * 递归解密对象中的敏感字段
 * @param obj 需要处理的对象
 * @param prefix 字段路径前缀
 * @returns 处理后的对象
 */
function decryptSensitiveFields(obj: any, prefix: string = ''): any {
  if (obj === null || obj === undefined) return obj
  
  if (typeof obj !== 'object') {
    return isSensitiveField(prefix) ? CryptoUtil.decrypt(String(obj)) : obj
  }
  
  if (Array.isArray(obj)) {
    return obj.map((item, index) => 
      decryptSensitiveFields(item, `${prefix}[${index}]`)
    )
  }
  
  const result: any = {}
  for (const [key, value] of Object.entries(obj)) {
    const currentPath = prefix ? `${prefix}.${key}` : key
    result[key] = decryptSensitiveFields(value, currentPath)
  }
  
  return result
}

/**
 * 创建auth模块的持久化配置
 */
export const authPersistedState = createPersistedState({
  key: 'organ-auth-state',
  paths: ['auth'],
  storage: window.localStorage,
  setState: (key: string, state: any) => {
    try {
      // 对整个auth状态进行加密处理
      const encryptedState = encryptSensitiveFields(state, 'auth')
      window.localStorage.setItem(key, JSON.stringify(encryptedState))
    } catch (error) {
      console.error('Auth状态持久化失败:', error)
    }
  },
  getState: (key: string) => {
    try {
      const storedState = window.localStorage.getItem(key)
      if (!storedState) return undefined
      
      const parsedState = JSON.parse(storedState)
      // 对整个auth状态进行解密处理
      return decryptSensitiveFields(parsedState, 'auth')
    } catch (error) {
      console.error('Auth状态恢复失败:', error)
      return undefined
    }
  }
})

/**
 * 创建canteenUser模块的持久化配置
 */
export const canteenUserPersistedState = createPersistedState({
  key: 'organ-canteen-user-state',
  paths: ['canteenUser'],
  storage: window.localStorage,
  setState: (key: string, state: any) => {
    try {
      // 对整个canteenUser状态进行加密处理
      const encryptedState = encryptSensitiveFields(state, 'canteenUser')
      window.localStorage.setItem(key, JSON.stringify(encryptedState))
    } catch (error) {
      console.error('CanteenUser状态持久化失败:', error)
    }
  },
  getState: (key: string) => {
    try {
      const storedState = window.localStorage.getItem(key)
      if (!storedState) return undefined
      
      const parsedState = JSON.parse(storedState)
      // 对整个canteenUser状态进行解密处理
      return decryptSensitiveFields(parsedState, 'canteenUser')
    } catch (error) {
      console.error('CanteenUser状态恢复失败:', error)
      return undefined
    }
  }
})

/**
 * 通用持久化配置（用于非敏感数据）
 */
export const generalPersistedState = createPersistedState({
  key: 'organ-general-state',
  storage: window.localStorage,
  // 排除敏感模块，只持久化非敏感数据
  filter: (mutation) => {
    return !['auth/', 'canteenUser/'].some(prefix => 
      mutation.type.startsWith(prefix)
    )
  }
})
