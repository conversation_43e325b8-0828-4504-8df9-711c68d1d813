import CryptoJS from 'crypto-js'

/**
 * 加密解密工具类
 * 用于敏感数据的加密存储和解密读取
 */
class CryptoUtil {
  // 默认密钥，实际项目中应该从环境变量或配置文件中获取
  private static readonly SECRET_KEY = 'organ-smart-platform-2024'
  
  /**
   * 加密字符串
   * @param text 需要加密的文本
   * @returns 加密后的字符串
   */
  static encrypt(text: string): string {
    try {
      if (!text) return ''
      return CryptoJS.AES.encrypt(text, this.SECRET_KEY).toString()
    } catch (error) {
      console.error('加密失败:', error)
      return text
    }
  }

  /**
   * 解密字符串
   * @param encryptedText 加密的文本
   * @returns 解密后的字符串
   */
  static decrypt(encryptedText: string): string {
    try {
      if (!encryptedText) return ''
      const bytes = CryptoJS.AES.decrypt(encryptedText, this.SECRET_KEY)
      return bytes.toString(CryptoJS.enc.Utf8)
    } catch (error) {
      console.error('解密失败:', error)
      return encryptedText
    }
  }

  /**
   * 加密对象
   * @param obj 需要加密的对象
   * @returns 加密后的字符串
   */
  static encryptObject(obj: any): string {
    try {
      if (!obj) return ''
      const jsonString = JSON.stringify(obj)
      return this.encrypt(jsonString)
    } catch (error) {
      console.error('对象加密失败:', error)
      return JSON.stringify(obj)
    }
  }

  /**
   * 解密对象
   * @param encryptedText 加密的文本
   * @returns 解密后的对象
   */
  static decryptObject<T = any>(encryptedText: string): T | null {
    try {
      if (!encryptedText) return null
      const decryptedString = this.decrypt(encryptedText)
      return JSON.parse(decryptedString)
    } catch (error) {
      console.error('对象解密失败:', error)
      return null
    }
  }

  /**
   * 生成随机密钥
   * @param length 密钥长度
   * @returns 随机密钥
   */
  static generateRandomKey(length: number = 32): string {
    return CryptoJS.lib.WordArray.random(length).toString()
  }

  /**
   * 计算字符串的MD5哈希值
   * @param text 需要计算哈希的文本
   * @returns MD5哈希值
   */
  static md5(text: string): string {
    return CryptoJS.MD5(text).toString()
  }

  /**
   * 计算字符串的SHA256哈希值
   * @param text 需要计算哈希的文本
   * @returns SHA256哈希值
   */
  static sha256(text: string): string {
    return CryptoJS.SHA256(text).toString()
  }
}

export default CryptoUtil
