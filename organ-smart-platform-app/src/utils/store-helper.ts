import store from '@/store'
import { IUserInfo } from '@/store/modules/auth'
import { ICanteenUserInfo } from '@/store/modules/canteen/user'

/**
 * Store 辅助工具类
 * 提供便捷的状态管理操作方法
 */
export class StoreHelper {
  
  // ==================== Auth 模块相关方法 ====================
  
  /**
   * 用户登录
   * @param loginData 登录数据
   */
  static async authLogin(loginData: {
    token: string;
    accessToken?: string;
    refreshToken?: string;
    userInfo?: IUserInfo;
    permissions?: string[];
    roles?: string[];
    expiresIn?: number;
  }) {
    return store.dispatch('auth/login', loginData)
  }
  
  /**
   * 用户登出
   */
  static async authLogout() {
    return store.dispatch('auth/logout')
  }
  
  /**
   * 刷新token
   * @param tokenData 新的token数据
   */
  static async authRefreshToken(tokenData: {
    token: string;
    accessToken?: string;
    expiresIn?: number;
  }) {
    return store.dispatch('auth/refreshToken', tokenData)
  }
  
  /**
   * 更新用户信息
   * @param userInfo 用户信息
   */
  static async authUpdateUserInfo(userInfo: IUserInfo) {
    return store.dispatch('auth/updateUserInfo', userInfo)
  }
  
  /**
   * 获取用户是否已登录
   */
  static get isAuthAuthenticated(): boolean {
    return store.getters['auth/isAuthenticated']
  }
  
  /**
   * 获取用户显示名称
   */
  static get authUserDisplayName(): string {
    return store.getters['auth/userDisplayName']
  }
  
  /**
   * 检查用户权限
   * @param permission 权限名称
   */
  static hasAuthPermission(permission: string): boolean {
    return store.getters['auth/hasPermission'](permission)
  }
  
  /**
   * 检查用户角色
   * @param role 角色名称
   */
  static hasAuthRole(role: string): boolean {
    return store.getters['auth/hasRole'](role)
  }
  
  /**
   * 获取auth token
   */
  static get authToken(): string {
    return store.state.auth.token
  }
  
  /**
   * 获取auth用户信息
   */
  static get authUserInfo(): IUserInfo {
    return store.state.auth.userInfo
  }
  
  // ==================== CanteenUser 模块相关方法 ====================
  
  /**
   * 食堂用户登录
   * @param loginData 登录数据
   */
  static async canteenUserLogin(loginData: {
    token: string;
    accessToken?: string;
    refreshToken?: string;
    userInfo?: ICanteenUserInfo;
    permissions?: string[];
    roles?: string[];
    expiresIn?: number;
  }) {
    return store.dispatch('canteenUser/login', loginData)
  }
  
  /**
   * 食堂用户登出
   */
  static async canteenUserLogout() {
    return store.dispatch('canteenUser/logout')
  }
  
  /**
   * 选择食堂
   * @param canteenId 食堂ID
   */
  static async selectCanteen(canteenId: string | number) {
    return store.dispatch('canteenUser/selectCanteen', canteenId)
  }
  
  /**
   * 添加商品到购物车
   * @param item 商品信息
   */
  static async addToCart(item: any) {
    return store.dispatch('canteenUser/addToCart', item)
  }
  
  /**
   * 从购物车移除商品
   * @param itemId 商品ID
   */
  static async removeFromCart(itemId: string | number) {
    return store.dispatch('canteenUser/removeFromCart', itemId)
  }
  
  /**
   * 清空购物车
   */
  static async clearCart() {
    return store.dispatch('canteenUser/clearCart')
  }
  
  /**
   * 更新用户余额
   * @param balance 新余额
   */
  static async updateCanteenUserBalance(balance: number) {
    return store.dispatch('canteenUser/updateBalance', balance)
  }
  
  /**
   * 获取食堂用户是否已登录
   */
  static get isCanteenUserAuthenticated(): boolean {
    return store.getters['canteenUser/isAuthenticated']
  }
  
  /**
   * 获取食堂用户显示名称
   */
  static get canteenUserDisplayName(): string {
    return store.getters['canteenUser/userDisplayName']
  }
  
  /**
   * 获取食堂用户余额
   */
  static get canteenUserBalance(): number {
    return store.getters['canteenUser/userBalance']
  }
  
  /**
   * 获取购物车商品数量
   */
  static get cartItemCount(): number {
    return store.getters['canteenUser/cartItemCount']
  }
  
  /**
   * 获取购物车总金额
   */
  static get cartTotalAmount(): number {
    return store.getters['canteenUser/cartTotalAmount']
  }
  
  /**
   * 获取当前选择的食堂ID
   */
  static get currentCanteenId(): string | number {
    return store.state.canteenUser.currentCanteenId
  }
  
  /**
   * 获取购物车信息
   */
  static get cartInfo() {
    return store.state.canteenUser.cart
  }
  
  /**
   * 检查食堂用户权限
   * @param permission 权限名称
   */
  static hasCanteenUserPermission(permission: string): boolean {
    return store.getters['canteenUser/hasPermission'](permission)
  }
  
  /**
   * 检查食堂用户角色
   * @param role 角色名称
   */
  static hasCanteenUserRole(role: string): boolean {
    return store.getters['canteenUser/hasRole'](role)
  }
  
  // ==================== 通用方法 ====================
  
  /**
   * 清除所有状态（用于完全退出登录）
   */
  static async clearAllState() {
    await Promise.all([
      this.authLogout(),
      this.canteenUserLogout()
    ])
  }
  
  /**
   * 检查任一模块是否已登录
   */
  static get isAnyUserAuthenticated(): boolean {
    return this.isAuthAuthenticated || this.isCanteenUserAuthenticated
  }
}

export default StoreHelper
