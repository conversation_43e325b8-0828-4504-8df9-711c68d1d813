<template>
  <div class="store-usage-example">
    <h2>状态管理使用示例</h2>

    <!-- Auth 模块示例 -->
    <div class="auth-section">
      <h3>权限管理模块 (Auth)</h3>
      <div class="info-panel">
        <p>登录状态: {{ isAuthAuthenticated ? "已登录" : "未登录" }}</p>
        <p v-if="isAuthAuthenticated">用户名: {{ authUserDisplayName }}</p>
        <p v-if="isAuthAuthenticated">
          Token: {{ authToken.substring(0, 20) }}...
        </p>
      </div>

      <div class="action-buttons">
        <el-button
          v-if="!isAuthAuthenticated"
          type="primary"
          @click="handleAuthLogin"
        >
          模拟登录
        </el-button>
        <el-button
          v-if="isAuthAuthenticated"
          type="danger"
          @click="handleAuthLogout"
        >
          退出登录
        </el-button>
        <el-button
          v-if="isAuthAuthenticated"
          type="info"
          @click="handleUpdateUserInfo"
        >
          更新用户信息
        </el-button>
      </div>
    </div>

    <!-- CanteenUser 模块示例 -->
    <div class="canteen-section">
      <h3>智慧食堂用户模块 (CanteenUser)</h3>
      <div class="info-panel">
        <p>登录状态: {{ isCanteenUserAuthenticated ? "已登录" : "未登录" }}</p>
        <p v-if="isCanteenUserAuthenticated">
          用户名: {{ canteenUserDisplayName }}
        </p>
        <p v-if="isCanteenUserAuthenticated">余额: ¥{{ canteenUserBalance }}</p>
        <p v-if="isCanteenUserAuthenticated">
          当前食堂: {{ currentCanteenId || "未选择" }}
        </p>
        <p>购物车商品数量: {{ cartItemCount }}</p>
        <p>购物车总金额: ¥{{ cartTotalAmount }}</p>
      </div>

      <div class="action-buttons">
        <el-button
          v-if="!isCanteenUserAuthenticated"
          type="primary"
          @click="handleCanteenUserLogin"
        >
          模拟登录
        </el-button>
        <el-button
          v-if="isCanteenUserAuthenticated"
          type="danger"
          @click="handleCanteenUserLogout"
        >
          退出登录
        </el-button>
        <el-button
          v-if="isCanteenUserAuthenticated"
          type="info"
          @click="handleSelectCanteen"
        >
          选择食堂
        </el-button>
        <el-button type="success" @click="handleAddToCart">
          添加到购物车
        </el-button>
        <el-button type="warning" @click="handleClearCart">
          清空购物车
        </el-button>
      </div>
    </div>

    <!-- 购物车详情 -->
    <div v-if="cartInfo.items.length > 0" class="cart-section">
      <h3>购物车详情</h3>
      <div class="cart-items">
        <div v-for="item in cartInfo.items" :key="item.id" class="cart-item">
          <span>{{ item.name }}</span>
          <span>数量: {{ item.quantity }}</span>
          <span>单价: ¥{{ item.price }}</span>
          <span>小计: ¥{{ item.price * item.quantity }}</span>
          <el-button
            size="mini"
            type="danger"
            @click="handleRemoveFromCart(item.id)"
          >
            移除
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import StoreHelper from "@/utils/store-helper";

export default defineComponent({
  name: "StoreUsageExample",

  computed: {
    isAuthAuthenticated(): boolean {
      return StoreHelper.isAuthAuthenticated;
    },

    authUserDisplayName(): string {
      return StoreHelper.authUserDisplayName;
    },

    authToken(): string {
      return StoreHelper.authToken;
    },

    isCanteenUserAuthenticated(): boolean {
      return StoreHelper.isCanteenUserAuthenticated;
    },

    canteenUserDisplayName(): string {
      return StoreHelper.canteenUserDisplayName;
    },

    canteenUserBalance(): number {
      return StoreHelper.canteenUserBalance;
    },

    currentCanteenId(): string | number {
      return StoreHelper.currentCanteenId;
    },

    cartItemCount(): number {
      return StoreHelper.cartItemCount;
    },

    cartTotalAmount(): number {
      return StoreHelper.cartTotalAmount;
    },

    cartInfo() {
      return StoreHelper.cartInfo;
    },
  },

  methods: {
    /**
     * 处理权限模块登录
     */
    async handleAuthLogin() {
      try {
        await StoreHelper.authLogin({
          token: "mock-auth-token-" + Date.now(),
          accessToken: "mock-access-token-" + Date.now(),
          refreshToken: "mock-refresh-token-" + Date.now(),
          userInfo: {
            id: 1,
            username: "admin",
            realName: "管理员",
            email: "<EMAIL>",
            phone: "13800138000",
            deptName: "技术部",
          },
          permissions: ["user:read", "user:write", "system:admin"],
          roles: ["admin", "user"],
          expiresIn: 7200, // 2小时
        });
        this.$message.success("权限模块登录成功");
      } catch (error) {
        this.$message.error("权限模块登录失败");
        console.error(error);
      }
    },

    /**
     * 处理权限模块登出
     */
    async handleAuthLogout() {
      try {
        await StoreHelper.authLogout();
        this.$message.success("权限模块登出成功");
      } catch (error) {
        this.$message.error("权限模块登出失败");
        console.error(error);
      }
    },

    /**
     * 处理更新用户信息
     */
    async handleUpdateUserInfo() {
      try {
        await StoreHelper.authUpdateUserInfo({
          ...StoreHelper.authUserInfo,
          realName: "管理员(已更新)",
          lastLoginTime: new Date().toISOString(),
        });
        this.$message.success("用户信息更新成功");
      } catch (error) {
        this.$message.error("用户信息更新失败");
        console.error(error);
      }
    },

    /**
     * 处理食堂用户登录
     */
    async handleCanteenUserLogin() {
      try {
        await StoreHelper.canteenUserLogin({
          token: "mock-canteen-token-" + Date.now(),
          accessToken: "mock-canteen-access-token-" + Date.now(),
          userInfo: {
            id: 1001,
            username: "canteen_user",
            realName: "张三",
            phone: "13900139000",
            employeeNo: "EMP001",
            deptName: "研发部",
            balance: 500.0,
            totalConsumption: 1200.5,
            memberLevel: "VIP",
          },
          permissions: ["canteen:order", "canteen:pay"],
          roles: ["canteen_user"],
          expiresIn: 3600, // 1小时
        });
        this.$message.success("食堂用户登录成功");
      } catch (error) {
        this.$message.error("食堂用户登录失败");
        console.error(error);
      }
    },

    /**
     * 处理食堂用户登出
     */
    async handleCanteenUserLogout() {
      try {
        await StoreHelper.canteenUserLogout();
        this.$message.success("食堂用户登出成功");
      } catch (error) {
        this.$message.error("食堂用户登出失败");
        console.error(error);
      }
    },

    /**
     * 处理选择食堂
     */
    async handleSelectCanteen() {
      try {
        const canteenId = Math.floor(Math.random() * 5) + 1;
        await StoreHelper.selectCanteen(canteenId);
        this.$message.success(`已选择食堂 ${canteenId}`);
      } catch (error) {
        this.$message.error("选择食堂失败");
        console.error(error);
      }
    },

    /**
     * 处理添加到购物车
     */
    async handleAddToCart() {
      try {
        const mockItem = {
          id: Date.now(),
          name: `商品${Math.floor(Math.random() * 100)}`,
          price: Math.floor(Math.random() * 50) + 10,
          quantity: 1,
        };
        await StoreHelper.addToCart(mockItem);
        this.$message.success(`已添加 ${mockItem.name} 到购物车`);
      } catch (error) {
        this.$message.error("添加到购物车失败");
        console.error(error);
      }
    },

    /**
     * 处理从购物车移除商品
     */
    async handleRemoveFromCart(itemId: string | number) {
      try {
        await StoreHelper.removeFromCart(itemId);
        this.$message.success("已从购物车移除商品");
      } catch (error) {
        this.$message.error("从购物车移除失败");
        console.error(error);
      }
    },

    /**
     * 处理清空购物车
     */
    async handleClearCart() {
      try {
        await StoreHelper.clearCart();
        this.$message.success("购物车已清空");
      } catch (error) {
        this.$message.error("清空购物车失败");
        console.error(error);
      }
    },
  },
});
</script>

<style scoped>
.store-usage-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.auth-section,
.canteen-section,
.cart-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.info-panel {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.info-panel p {
  margin: 5px 0;
  color: #606266;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.cart-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.cart-item span {
  flex: 1;
  text-align: center;
}
</style>
