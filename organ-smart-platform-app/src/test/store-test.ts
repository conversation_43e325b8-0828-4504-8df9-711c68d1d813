/**
 * 状态管理功能测试
 * 用于验证auth和canteenUser模块的基本功能
 */

import StoreHelper from '@/utils/store-helper'
import CryptoUtil from '@/utils/crypto'

/**
 * 测试加密解密功能
 */
export function testCrypto() {
  console.log('=== 测试加密解密功能 ===')
  
  // 测试字符串加密解密
  const originalText = 'test-token-12345'
  const encrypted = CryptoUtil.encrypt(originalText)
  const decrypted = CryptoUtil.decrypt(encrypted)
  
  console.log('原始文本:', originalText)
  console.log('加密后:', encrypted)
  console.log('解密后:', decrypted)
  console.log('加密解密是否成功:', originalText === decrypted)
  
  // 测试对象加密解密
  const originalObj = {
    token: 'test-token',
    userInfo: {
      id: 1,
      username: 'testuser',
      phone: '13800138000'
    }
  }
  
  const encryptedObj = CryptoUtil.encryptObject(originalObj)
  const decryptedObj = CryptoUtil.decryptObject(encryptedObj)
  
  console.log('原始对象:', originalObj)
  console.log('加密后:', encryptedObj)
  console.log('解密后:', decryptedObj)
  console.log('对象加密解密是否成功:', JSON.stringify(originalObj) === JSON.stringify(decryptedObj))
}

/**
 * 测试Auth模块功能
 */
export async function testAuthModule() {
  console.log('=== 测试Auth模块功能 ===')
  
  try {
    // 测试登录前状态
    console.log('登录前状态:')
    console.log('- 是否已登录:', StoreHelper.isAuthAuthenticated)
    console.log('- 用户名:', StoreHelper.authUserDisplayName)
    console.log('- Token:', StoreHelper.authToken)
    
    // 测试登录
    console.log('\n执行登录...')
    await StoreHelper.authLogin({
      token: 'test-auth-token-' + Date.now(),
      accessToken: 'test-access-token-' + Date.now(),
      refreshToken: 'test-refresh-token-' + Date.now(),
      userInfo: {
        id: 1,
        username: 'admin',
        realName: '管理员',
        email: '<EMAIL>',
        phone: '13800138000'
      },
      permissions: ['user:read', 'user:write', 'system:admin'],
      roles: ['admin', 'user'],
      expiresIn: 7200
    })
    
    // 测试登录后状态
    console.log('登录后状态:')
    console.log('- 是否已登录:', StoreHelper.isAuthAuthenticated)
    console.log('- 用户名:', StoreHelper.authUserDisplayName)
    console.log('- Token:', StoreHelper.authToken.substring(0, 20) + '...')
    console.log('- 权限检查(user:read):', StoreHelper.hasAuthPermission('user:read'))
    console.log('- 权限检查(invalid):', StoreHelper.hasAuthPermission('invalid'))
    console.log('- 角色检查(admin):', StoreHelper.hasAuthRole('admin'))
    console.log('- 角色检查(guest):', StoreHelper.hasAuthRole('guest'))
    
    // 测试更新用户信息
    console.log('\n更新用户信息...')
    await StoreHelper.authUpdateUserInfo({
      ...StoreHelper.authUserInfo,
      realName: '管理员(已更新)',
      lastLoginTime: new Date().toISOString()
    })
    console.log('更新后用户名:', StoreHelper.authUserDisplayName)
    
    // 测试登出
    console.log('\n执行登出...')
    await StoreHelper.authLogout()
    console.log('登出后状态:')
    console.log('- 是否已登录:', StoreHelper.isAuthAuthenticated)
    console.log('- 用户名:', StoreHelper.authUserDisplayName)
    console.log('- Token:', StoreHelper.authToken)
    
  } catch (error) {
    console.error('Auth模块测试失败:', error)
  }
}

/**
 * 测试CanteenUser模块功能
 */
export async function testCanteenUserModule() {
  console.log('=== 测试CanteenUser模块功能 ===')
  
  try {
    // 测试登录前状态
    console.log('登录前状态:')
    console.log('- 是否已登录:', StoreHelper.isCanteenUserAuthenticated)
    console.log('- 用户名:', StoreHelper.canteenUserDisplayName)
    console.log('- 余额:', StoreHelper.canteenUserBalance)
    console.log('- 购物车商品数:', StoreHelper.cartItemCount)
    console.log('- 购物车总金额:', StoreHelper.cartTotalAmount)
    
    // 测试登录
    console.log('\n执行登录...')
    await StoreHelper.canteenUserLogin({
      token: 'test-canteen-token-' + Date.now(),
      accessToken: 'test-canteen-access-token-' + Date.now(),
      userInfo: {
        id: 1001,
        username: 'canteen_user',
        realName: '张三',
        phone: '13900139000',
        employeeNo: 'EMP001',
        balance: 500.00,
        totalConsumption: 1200.50,
        memberLevel: 'VIP'
      },
      permissions: ['canteen:order', 'canteen:pay'],
      roles: ['canteen_user'],
      expiresIn: 3600
    })
    
    // 测试登录后状态
    console.log('登录后状态:')
    console.log('- 是否已登录:', StoreHelper.isCanteenUserAuthenticated)
    console.log('- 用户名:', StoreHelper.canteenUserDisplayName)
    console.log('- 余额:', StoreHelper.canteenUserBalance)
    
    // 测试选择食堂
    console.log('\n选择食堂...')
    await StoreHelper.selectCanteen(1)
    console.log('当前食堂ID:', StoreHelper.currentCanteenId)
    
    // 测试添加商品到购物车
    console.log('\n添加商品到购物车...')
    await StoreHelper.addToCart({
      id: 1,
      name: '宫保鸡丁',
      price: 25.5,
      quantity: 2
    })
    
    await StoreHelper.addToCart({
      id: 2,
      name: '红烧肉',
      price: 30.0,
      quantity: 1
    })
    
    console.log('购物车状态:')
    console.log('- 商品数量:', StoreHelper.cartItemCount)
    console.log('- 总金额:', StoreHelper.cartTotalAmount)
    console.log('- 商品详情:', StoreHelper.cartInfo.items)
    
    // 测试移除商品
    console.log('\n移除商品...')
    await StoreHelper.removeFromCart(1)
    console.log('移除后购物车状态:')
    console.log('- 商品数量:', StoreHelper.cartItemCount)
    console.log('- 总金额:', StoreHelper.cartTotalAmount)
    
    // 测试更新余额
    console.log('\n更新余额...')
    await StoreHelper.updateCanteenUserBalance(450.00)
    console.log('更新后余额:', StoreHelper.canteenUserBalance)
    
    // 测试清空购物车
    console.log('\n清空购物车...')
    await StoreHelper.clearCart()
    console.log('清空后购物车状态:')
    console.log('- 商品数量:', StoreHelper.cartItemCount)
    console.log('- 总金额:', StoreHelper.cartTotalAmount)
    
    // 测试登出
    console.log('\n执行登出...')
    await StoreHelper.canteenUserLogout()
    console.log('登出后状态:')
    console.log('- 是否已登录:', StoreHelper.isCanteenUserAuthenticated)
    console.log('- 用户名:', StoreHelper.canteenUserDisplayName)
    console.log('- 余额:', StoreHelper.canteenUserBalance)
    
  } catch (error) {
    console.error('CanteenUser模块测试失败:', error)
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('开始运行状态管理功能测试...\n')
  
  // 测试加密解密
  testCrypto()
  console.log('\n')
  
  // 测试Auth模块
  await testAuthModule()
  console.log('\n')
  
  // 测试CanteenUser模块
  await testCanteenUserModule()
  
  console.log('\n所有测试完成！')
}

// 如果在浏览器环境中，可以在控制台调用测试
if (typeof window !== 'undefined') {
  (window as any).storeTest = {
    testCrypto,
    testAuthModule,
    testCanteenUserModule,
    runAllTests
  }
  
  console.log('状态管理测试函数已挂载到 window.storeTest')
  console.log('可以在控制台中调用:')
  console.log('- window.storeTest.testCrypto() // 测试加密解密')
  console.log('- window.storeTest.testAuthModule() // 测试Auth模块')
  console.log('- window.storeTest.testCanteenUserModule() // 测试CanteenUser模块')
  console.log('- window.storeTest.runAllTests() // 运行所有测试')
}
