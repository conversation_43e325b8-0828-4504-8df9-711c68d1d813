# 状态管理使用指南

本文档介绍了 organ-smart-platform-app 项目中的状态管理功能，包括权限管理(Auth)和智慧食堂用户(CanteenUser)模块的使用方法。

## 功能特性

### 🔐 安全特性
- **敏感数据加密**: token、accessToken、refreshToken 等敏感信息在本地存储时自动加密
- **自动解密**: 从本地存储读取时自动解密
- **数据持久化**: 使用 vuex-persistedstate 实现状态持久化
- **分模块存储**: 不同模块的数据分别存储，互不影响

### 📦 已安装依赖
- `vuex-persistedstate@^4.1.0`: Vuex 状态持久化
- `crypto-js@^4.2.0`: 加密解密功能
- `@types/crypto-js@^4.2.2`: TypeScript 类型定义

## 模块结构

```
src/store/
├── index.ts                    # Store 主入口
├── modules/
│   ├── auth.ts                # 权限管理模块
│   └── canteen/
│       └── user.ts            # 智慧食堂用户模块
└── plugins/
    └── persistedstate.ts      # 持久化配置
```

## 使用方法

### 1. 基础使用

#### 在组件中直接使用 Store

```typescript
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class MyComponent extends Vue {
  // 获取登录状态
  get isAuthenticated(): boolean {
    return this.$store.getters['auth/isAuthenticated']
  }
  
  // 获取用户信息
  get userInfo() {
    return this.$store.state.auth.userInfo
  }
  
  // 登录
  async login() {
    await this.$store.dispatch('auth/login', {
      token: 'your-token',
      userInfo: { username: 'user' },
      expiresIn: 3600
    })
  }
  
  // 登出
  async logout() {
    await this.$store.dispatch('auth/logout')
  }
}
```

#### 使用 StoreHelper 工具类（推荐）

```typescript
import StoreHelper from '@/utils/store-helper'

// 权限模块登录
await StoreHelper.authLogin({
  token: 'your-token',
  userInfo: { username: 'user' },
  permissions: ['read', 'write'],
  expiresIn: 3600
})

// 检查登录状态
const isLoggedIn = StoreHelper.isAuthAuthenticated

// 检查权限
const hasPermission = StoreHelper.hasAuthPermission('read')

// 食堂用户登录
await StoreHelper.canteenUserLogin({
  token: 'canteen-token',
  userInfo: { username: 'canteen_user', balance: 100 }
})

// 添加商品到购物车
await StoreHelper.addToCart({
  id: 1,
  name: '商品名称',
  price: 25.5,
  quantity: 2
})
```

### 2. Auth 模块功能

#### 状态字段
- `token`: 用户登录token
- `accessToken`: 访问token
- `refreshToken`: 刷新token
- `userInfo`: 用户信息
- `permissions`: 用户权限列表
- `roles`: 用户角色列表
- `isLoggedIn`: 是否已登录
- `loginTime`: 登录时间戳
- `tokenExpireTime`: token过期时间戳

#### 主要方法
- `login(loginData)`: 用户登录
- `logout()`: 用户登出
- `refreshToken(tokenData)`: 刷新token
- `updateUserInfo(userInfo)`: 更新用户信息

#### Getters
- `isAuthenticated`: 检查是否已登录
- `userDisplayName`: 获取用户显示名称
- `hasPermission(permission)`: 检查权限
- `hasRole(role)`: 检查角色

### 3. CanteenUser 模块功能

#### 状态字段
- `token`: 用户登录token
- `userInfo`: 用户信息（包含余额等食堂相关信息）
- `currentCanteenId`: 当前选择的食堂ID
- `cart`: 购物车信息
  - `items`: 商品列表
  - `totalAmount`: 总金额
  - `totalCount`: 总数量

#### 主要方法
- `login(loginData)`: 用户登录
- `logout()`: 用户登出
- `selectCanteen(canteenId)`: 选择食堂
- `addToCart(item)`: 添加到购物车
- `removeFromCart(itemId)`: 从购物车移除
- `clearCart()`: 清空购物车
- `updateBalance(balance)`: 更新余额

#### Getters
- `isAuthenticated`: 检查是否已登录
- `userDisplayName`: 获取用户显示名称
- `userBalance`: 获取用户余额
- `cartItemCount`: 获取购物车商品数量
- `cartTotalAmount`: 获取购物车总金额

### 4. 数据持久化

#### 自动持久化
所有状态数据会自动持久化到 localStorage，包括：
- Auth 模块数据存储在 `organ-auth-state`
- CanteenUser 模块数据存储在 `organ-canteen-user-state`
- 其他通用数据存储在 `organ-general-state`

#### 敏感数据加密
以下字段会自动加密存储：
- `token`
- `accessToken`
- `refreshToken`
- `userInfo.password`
- `userInfo.phone`
- `userInfo.email`
- `userInfo.idCard`

#### 加密工具
可以直接使用加密工具类：

```typescript
import CryptoUtil from '@/utils/crypto'

// 加密字符串
const encrypted = CryptoUtil.encrypt('sensitive data')

// 解密字符串
const decrypted = CryptoUtil.decrypt(encrypted)

// 加密对象
const encryptedObj = CryptoUtil.encryptObject({ key: 'value' })

// 解密对象
const decryptedObj = CryptoUtil.decryptObject(encryptedObj)
```

## 示例组件

查看 `src/components/examples/StoreUsageExample.vue` 文件，了解完整的使用示例。

## 注意事项

1. **版本兼容性**: 所有依赖都已选择与 Vue 2.7.16 和 Vuex 3.6.2 兼容的版本
2. **安全性**: 敏感数据会自动加密，但请确保不要在控制台或日志中输出敏感信息
3. **性能**: 状态变化会自动持久化，频繁的状态更新可能影响性能
4. **清理**: 用户登出时会自动清理相关状态和本地存储

## 开发建议

1. 优先使用 `StoreHelper` 工具类，它提供了更简洁的API
2. 在组件中使用计算属性来响应状态变化
3. 异步操作使用 actions，同步操作使用 mutations
4. 定期检查 token 过期时间，及时刷新或重新登录
5. 在生产环境中替换默认的加密密钥
