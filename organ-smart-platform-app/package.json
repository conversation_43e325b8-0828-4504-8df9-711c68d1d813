{"name": "hncrc-job-report-web", "type": "module", "version": "1.0.0", "description": "HNCRC Organ Smart Platform Web Application", "author": "", "license": "MIT", "keywords": ["vue", "typescript", "vue-router", "webpack"], "main": "index.js", "scripts": {"dev": "webpack serve", "build": "webpack --mode production", "build:prod": "webpack --mode production", "build:pre": "webpack --mode production", "build:sit": "webpack --mode production", "build:dev": "webpack --mode development", "serve": "webpack serve --mode development --open", "type-check": "tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@types/crypto-js": "^4.2.2", "@types/qrcode": "^1.5.5", "axios": "^1.10.0", "core-js": "^3.43.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-ui": "^2.15.14", "lodash": "^4.17.21", "qrcode": "^1.5.4", "vue": "^2.7.16", "vue-router": "^3.6.5", "vue-wordcloud": "^1.1.1", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@liuarui/postcss-px-to-viewport": "^1.0.1", "@types/lodash": "^4.17.19", "@types/node": "^20.10.0", "@types/vue-router": "^2.0.0", "@types/webpack-env": "^1.18.8", "babel-loader": "^9.1.3", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "css-loader": "^6.8.1", "dotenv": "^16.6.0", "html-webpack-plugin": "^5.5.3", "jiti": "^2.4.2", "sass": "^1.89.2", "sass-loader": "^16.0.5", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "typescript": "^5.2.2", "unplugin-auto-import": "^19.3.0", "vue-loader": "^15.11.1", "vue-template-compiler": "^2.7.16", "vue-tsc": "^1.8.27", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "volta": {"node": "18.19.0", "yarn": "1.22.22"}}