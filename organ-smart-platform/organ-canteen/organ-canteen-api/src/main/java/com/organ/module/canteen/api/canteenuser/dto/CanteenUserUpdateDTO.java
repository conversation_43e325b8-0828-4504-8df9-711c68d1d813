package com.organ.module.canteen.api.canteenuser.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 食堂用户更新请求对象
 */
@Data
public class CanteenUserUpdateDTO {
    
    /**
     * 用户ID（必填）
     * 用于标识要更新的用户
     */
    @NotNull(message = "用户ID不能为空")
    private Long id;
    
    /**
     * 用户姓名
     */
    private String username;
    
    /**
     * 用户手机号
     */
    private String mobile;
    
    /**
     * 用户所属单位/部门
     */
    private String dept;
    
    /**
     * 就餐食堂ID
     */
    private Long canteenId;
    
    /**
     * 就餐食堂编码
     */
    private String canteenCode;
    
    /**
     * 用户卡里余额（预留）
     */
    private BigDecimal balance;
    
    /**
     * 用户持卡类别
     * 1-一类、2-二类、3-三类
     */
    private String cardType;
    
    /**
     * 用户类型
     * REGULAR-编内、CONTRACT-编外、FAMILY-家属
     */
    private String userType;
    
    /**
     * 卡开通时间
     */
    private LocalDateTime cardOpenTime;
    
    /**
     * 卡过期时间（不设置时永久有效）
     */
    private LocalDateTime cardExpireTime;
    
    /**
     * 是否允许跨区域用餐
     * 0-不允许、1-允许
     */
    private Integer crossRegionFlag;
    
    /**
     * 就餐区域（关联字典表）
     */
    private String diningRegion;
    
    /**
     * 用户状态
     * ACTIVE-正常、DISABLED-禁用
     */
    private String status;
}