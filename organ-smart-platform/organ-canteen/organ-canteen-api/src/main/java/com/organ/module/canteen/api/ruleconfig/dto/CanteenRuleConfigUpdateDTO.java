package com.organ.module.canteen.api.ruleconfig.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 系统规则配置修改 DTO
 */
@ApiModel("系统规则配置修改 DTO")
@Data
public class CanteenRuleConfigUpdateDTO {

    /**
     * 规则配置ID
     */
    @ApiModelProperty(value = "规则配置ID", required = true, example = "1")
    @NotNull(message = "规则配置ID不能为空")
    private Long id;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称", required = true, example = "每日预约限制")
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 100, message = "规则名称长度不能超过100个字符")
    private String ruleName;
    
    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码", required = true, example = "DAILY_BOOKING_LIMIT")
    @NotBlank(message = "规则编码不能为空")
    @Size(max = 100, message = "规则编码长度不能超过100个字符")
    private String ruleCode;
    
    /**
     * 规则阈值
     */
    @ApiModelProperty(value = "规则阈值", required = true, example = "3")
    @NotNull(message = "规则阈值不能为空")
    private Integer ruleValue;
    
    /**
     * 时间单位
     */
    @ApiModelProperty(value = "时间单位", required = true, example = "DAY", notes = "DAY-天 WEEK-周 MONTH-月")
    @NotBlank(message = "时间单位不能为空")
    @Size(max = 20, message = "时间单位长度不能超过20个字符")
    private String timeUnit;
    
    /**
     * 规则描述
     */
    @ApiModelProperty(value = "规则描述", example = "用户每天最多可预约3次餐")
    @Size(max = 500, message = "规则描述长度不能超过500个字符")
    private String ruleDescription;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true, example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    @NotBlank(message = "状态不能为空")
    @Size(max = 20, message = "状态长度不能超过20个字符")
    private String status;

}