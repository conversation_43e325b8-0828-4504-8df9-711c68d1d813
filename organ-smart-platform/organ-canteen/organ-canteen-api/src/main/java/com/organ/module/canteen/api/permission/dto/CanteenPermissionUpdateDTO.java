package com.organ.module.canteen.api.permission.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 权限信息修改 DTO
 */
@ApiModel("权限信息修改 DTO")
@Data
public class CanteenPermissionUpdateDTO {

    /**
     * 权限ID
     */
    @ApiModelProperty(value = "权限ID", required = true, example = "1")
    @NotNull(message = "权限ID不能为空")
    private Long id;

    /**
     * 权限名称
     */
    @ApiModelProperty(value = "权限名称", required = true, example = "用户管理")
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String permissionName;
    
    /**
     * 权限编码
     */
    @ApiModelProperty(value = "权限编码", required = true, example = "user:manage")
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    private String permissionCode;
    
    /**
     * 权限类型
     */
    @ApiModelProperty(value = "权限类型", required = true, example = "MENU", notes = "MENU-菜单 BUTTON-按钮 API-接口")
    @NotBlank(message = "权限类型不能为空")
    @Size(max = 20, message = "权限类型长度不能超过20个字符")
    private String permissionType;
    
    /**
     * 父权限ID
     */
    @ApiModelProperty(value = "父权限ID", example = "1")
    private Long parentId;
    
    /**
     * 路由路径
     */
    @ApiModelProperty(value = "路由路径", example = "/user/manage")
    @Size(max = 200, message = "路由路径长度不能超过200个字符")
    private String path;
    
    /**
     * 组件路径
     */
    @ApiModelProperty(value = "组件路径", example = "user/UserManage")
    @Size(max = 200, message = "组件路径长度不能超过200个字符")
    private String component;
    
    /**
     * 图标
     */
    @ApiModelProperty(value = "图标", example = "user")
    @Size(max = 50, message = "图标长度不能超过50个字符")
    private String icon;
    
    /**
     * 排序序号
     */
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer sortOrder;
    
    /**
     * 是否显示
     */
    @ApiModelProperty(value = "是否显示", example = "1", notes = "0-隐藏 1-显示")
    private Integer visible;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true, example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    @NotBlank(message = "状态不能为空")
    @Size(max = 20, message = "状态长度不能超过20个字符")
    private String status;
    
    /**
     * 权限描述
     */
    @ApiModelProperty(value = "权限描述", example = "用户信息管理功能")
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    private String description;

}