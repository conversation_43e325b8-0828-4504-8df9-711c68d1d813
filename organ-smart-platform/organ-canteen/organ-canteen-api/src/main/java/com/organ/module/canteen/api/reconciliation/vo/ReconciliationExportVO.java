package com.organ.module.canteen.api.reconciliation.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ReconciliationExportVO {

    @ApiModelProperty(value = "就餐人员")
    @ExcelProperty("就餐人员")
    private String dinerName;

    @ApiModelProperty(value = "开卡食堂")
    @ExcelProperty("开卡食堂")
    private String cardedCanteen;

    @ApiModelProperty(value = "卡类别")
    @ExcelProperty("卡类别")
    private String cardType;

    @ApiModelProperty(value = "就餐食堂")
    @ExcelProperty("就餐食堂")
    private String mealCanteen;

    @ApiModelProperty(value = "就餐时间")
    @ExcelProperty("就餐时间")
    private LocalDateTime mealTime;

    @ApiModelProperty(value = "餐次")
    @ExcelProperty("餐次")
    private String mealPeriod;

    @ApiModelProperty(value = "用户类别")
    @ExcelProperty("用户类别")
    private String userType;

    @ApiModelProperty(value = "核销员")
    @ExcelProperty("核销员")
    private String checkInPerson;

    @ApiModelProperty(value = "状态")
    @ExcelProperty("状态")
    private String status;

}
