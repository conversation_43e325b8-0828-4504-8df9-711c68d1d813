package com.organ.module.canteen.api.booking.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class BookingExportVO {

    @ApiModelProperty(value = "id")
    @ExcelProperty("预约单号")
    private String id;

    @ApiModelProperty(value = "用户姓名")
    @ExcelProperty("用户姓名")
    private String username;

    @ApiModelProperty(value = "所属单位")
    @ExcelProperty("所属单位")
    private String belongUnit;

    @ApiModelProperty(value = "食堂名称")
    @ExcelProperty("食堂名称")
    private String canteenName;

    @ApiModelProperty(value = "餐次")
    @ExcelProperty("餐次")
    private String meal;

    @ApiModelProperty(value = "预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty("预约日期")
    private Date bookingDate;

    @ApiModelProperty(value = "使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty("使用时间")
    private Date usageDate;

    @ApiModelProperty(value = "状态")
    @ExcelProperty("状态")
    private String status;
}
