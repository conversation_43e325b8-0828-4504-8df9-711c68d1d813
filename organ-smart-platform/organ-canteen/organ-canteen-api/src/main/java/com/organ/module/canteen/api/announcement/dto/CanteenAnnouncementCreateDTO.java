package com.organ.module.canteen.api.announcement.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 公告创建参数
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
public class CanteenAnnouncementCreateDTO {

    /**
     * 公告标题
     */
    @NotBlank(message = "公告标题不能为空")
    private String title;

    /**
     * 公告内容
     */
    @NotBlank(message = "公告内容不能为空")
    private String content;

    /**
     * 状态（DRAFT-草稿 PUBLISHED-已发布 OFFLINE-已下线）
     */
    private String status;
}