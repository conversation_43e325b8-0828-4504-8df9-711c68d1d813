package com.organ.module.auth.api.vo;

import com.organ.module.canteen.api.canteenuser.vo.LoginUserInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三方登录响应VO
 *
 * <AUTHOR>
 */
@ApiModel("第三方登录响应")
@Data
public class AuthUserInfoVO {

    /**
     * 第三方系统的token
     */
    @ApiModelProperty("第三方系统的token")
    private String token;

    /**
     * 第三方刷新token
     */
    @ApiModelProperty("第三方刷新token")
    private String refreshToken;

    /**
     * 智慧食堂用户信息
     */
    @ApiModelProperty("智慧食堂用户信息")
    private LoginUserInfoVO userInfo;

    /**
     * token过期时间（秒）
     */
    @ApiModelProperty("token过期时间（秒）")
    private Long expiresIn;

}
