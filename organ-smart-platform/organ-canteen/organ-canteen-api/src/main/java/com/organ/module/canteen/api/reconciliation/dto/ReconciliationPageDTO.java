package com.organ.module.canteen.api.reconciliation.dto;

import com.hainancrc.framework.common.pojo.PageParam;
import com.organ.module.enums.ReconciliationTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReconciliationPageDTO extends PageParam {

    @ApiModelProperty(value = "食堂id")
    private Long canteenId;

    @ApiModelProperty(value = "类型(当餐、当日、当月)")
    private ReconciliationTypeEnum type;

    @ApiModelProperty(value = "开卡食堂")
    private String openingCanteenId;

    @ApiModelProperty(value = "就餐食堂")
    private String diningCanteenId;

    @ApiModelProperty(value = "餐次")
    private String mealPeriodId;

    @ApiModelProperty(value = "卡类别")
    private String cardType;
}
