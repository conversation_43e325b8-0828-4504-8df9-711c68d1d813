package com.organ.module.canteen.api.canteenuser.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.hainancrc.framework.common.pojo.PageParam;

/**
 * 食堂用户分页查询请求对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanteenUserPageDTO extends PageParam {
    
    /**
     * 用户姓名（模糊查询）
     */
    private String username;
    
    /**
     * 用户手机号（精确查询）
     */
    private String mobile;
    
    /**
     * 用户所属单位/部门（精确查询）
     */
    private String dept;
    
    /**
     * 就餐食堂ID（精确查询）
     */
    private Long canteenId;
    
    /**
     * 就餐食堂编码（精确查询）
     */
    private String canteenCode;
    
    /**
     * 用户持卡类别（精确查询）
     * 1-一类、2-二类、3-三类
     */
    private String cardType;
    
    /**
     * 用户类型（精确查询）
     * REGULAR-编内、CONTRACT-编外
     */
    private String userType;
    
    /**
     * 就餐区域（精确查询）
     */
    private String diningRegion;
    
    /**
     * 用户状态（精确查询）
     * ACTIVE-正常、DISABLED-禁用
     */
    private String status;
}