package com.organ.module.canteen.api.role.vo;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 角色信息响应 VO
 */
@ApiModel("角色信息响应 VO")
@Data
public class CanteenRoleRespVO {

    /**
     * 角色ID
     */
    @ApiModelProperty(value = "角色ID", example = "1")
    private Long id;
    
    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称", example = "系统管理员")
    private String roleName;
    
    /**
     * 角色编码
     */
    @ApiModelProperty(value = "角色编码", example = "SYSTEM_ADMIN")
    private String roleCode;
    
    /**
     * 角色描述
     */
    @ApiModelProperty(value = "角色描述", example = "系统管理员，拥有所有系统权限")
    private String description;
    
    /**
     * 用户数量
     */
    @ApiModelProperty(value = "用户数量", example = "5")
    private Integer userCount;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    private String status;
    
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "admin")
    private String creator;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updater;
    
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}