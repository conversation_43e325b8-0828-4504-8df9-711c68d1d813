package com.organ.module.canteen.api.ruleconfig.vo;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 系统规则配置响应 VO
 */
@ApiModel("系统规则配置响应 VO")
@Data
public class CanteenRuleConfigRespVO {

    /**
     * 规则配置ID
     */
    @ApiModelProperty(value = "规则配置ID", example = "1")
    private Long id;
    
    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称", example = "每日预约限制")
    private String ruleName;
    
    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码", example = "DAILY_BOOKING_LIMIT")
    private String ruleCode;
    
    /**
     * 规则阈值
     */
    @ApiModelProperty(value = "规则阈值", example = "3")
    private Integer ruleValue;
    
    /**
     * 时间单位
     */
    @ApiModelProperty(value = "时间单位", example = "DAY", notes = "DAY-天 WEEK-周 MONTH-月")
    private String timeUnit;
    
    /**
     * 规则描述
     */
    @ApiModelProperty(value = "规则描述", example = "用户每天最多可预约3次餐")
    private String ruleDescription;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    private String status;

    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述", example = "启用")
    private String statusDesc;

    /**
     * 时间单位描述
     */
    @ApiModelProperty(value = "时间单位描述", example = "天")
    private String timeUnitDesc;
    
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "admin")
    private String creator;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updater;
    
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}