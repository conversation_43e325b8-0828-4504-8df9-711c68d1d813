package com.organ.module.canteen.api.department.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 部门更新参数
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
public class CanteenDepartmentUpdateDTO {

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空")
    private Long id;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    private String departmentName;

    /**
     * 部门编码
     */
    @NotBlank(message = "部门编码不能为空")
    private String departmentCode;

    /**
     * 父部门ID
     */
    private Long parentId;
}