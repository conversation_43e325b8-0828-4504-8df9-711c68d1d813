package com.organ.module.canteen.api.canteenuser.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 食堂用户创建请求对象
 */
@Data
public class CanteenUserCreateDTO {
    
    /**
     * 用户姓名（必填）
     */
    @NotBlank(message = "用户姓名不能为空")
    private String username;
    
    /**
     * 用户手机号
     */
    private String mobile;
    
    /**
     * 用户所属单位/部门
     */
    private String dept;
    
    /**
     * 就餐食堂ID（必填）
     */
    @NotNull(message = "就餐食堂ID不能为空")
    private Long canteenId;
    
    /**
     * 就餐食堂编码（必填）
     */
    @NotBlank(message = "就餐食堂编码不能为空")
    private String canteenCode;
    
    /**
     * 用户卡里余额（预留字段）
     */
    private BigDecimal balance;
    
    /**
     * 用户持卡类别（必填）
     * 1-一类、2-二类、3-三类
     */
    @NotBlank(message = "用户持卡类别不能为空")
    private String cardType;
    
    /**
     * 用户类型（必填）
     * REGULAR-编内、CONTRACT-编外、FAMILY-家属
     */
    @NotBlank(message = "用户类型不能为空")
    private String userType;
    
    /**
     * 卡开通时间
     */
    private LocalDateTime cardOpenTime;
    
    /**
     * 卡过期时间（不设置时就是永久有效）
     */
    private LocalDateTime cardExpireTime;
    
    /**
     * 是否允许跨区域用餐
     * 0-不允许、1-允许
     */
    private Integer crossRegionFlag;
    
    /**
     * 就餐区域（必填）
     * 关联字典表 字典 dining_region
     */
    @NotBlank(message = "就餐区域不能为空")
    private String diningRegion;
    
    /**
     * 用户状态
     * ACTIVE-正常、DISABLED-禁用
     */
    private String status;
}