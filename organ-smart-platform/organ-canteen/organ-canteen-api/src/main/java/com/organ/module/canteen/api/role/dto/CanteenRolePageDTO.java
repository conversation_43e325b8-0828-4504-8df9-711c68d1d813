package com.organ.module.canteen.api.role.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.hainancrc.framework.common.pojo.PageParam;

import java.time.LocalDateTime;

/**
 * 角色信息分页 DTO
 */
@ApiModel("角色信息分页 DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CanteenRolePageDTO extends PageParam {

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称", example = "管理员")
    private String roleName;
    
    /**
     * 角色编码
     */
    @ApiModelProperty(value = "角色编码", example = "ADMIN")
    private String roleCode;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    private String status;
    
    /**
     * 创建时间范围
     */
    @ApiModelProperty(value = "创建时间范围")
    private LocalDateTime[] createTime;

}