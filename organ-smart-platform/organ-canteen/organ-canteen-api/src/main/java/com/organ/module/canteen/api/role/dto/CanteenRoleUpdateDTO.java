package com.organ.module.canteen.api.role.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 角色信息修改 DTO
 */
@ApiModel("角色信息修改 DTO")
@Data
public class CanteenRoleUpdateDTO {

    /**
     * 角色ID
     */
    @ApiModelProperty(value = "角色ID", required = true, example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long id;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称", required = true, example = "系统管理员")
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    private String roleName;
    
    /**
     * 角色编码
     */
    @ApiModelProperty(value = "角色编码", required = true, example = "SYSTEM_ADMIN")
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 100, message = "角色编码长度不能超过100个字符")
    private String roleCode;
    
    /**
     * 角色描述
     */
    @ApiModelProperty(value = "角色描述", example = "系统管理员，拥有所有系统权限")
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    private String description;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true, example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    @NotBlank(message = "状态不能为空")
    @Size(max = 20, message = "状态长度不能超过20个字符")
    private String status;

}