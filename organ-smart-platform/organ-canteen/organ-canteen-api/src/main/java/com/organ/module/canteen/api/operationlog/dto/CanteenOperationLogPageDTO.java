package com.organ.module.canteen.api.operationlog.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.hainancrc.framework.common.pojo.PageParam;
import java.time.LocalDateTime;

/**
 * 操作日志分页查询请求对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanteenOperationLogPageDTO extends PageParam {
    
    /**
     * 操作人（精确查询）
     * 按操作人筛选日志记录
     */
    private String operator;
    
    /**
     * 操作类型（精确查询）
     * 按操作类型筛选，如：登录、新增、修改、删除等
     */
    private String operationType;
    
    
    /**
     * 开始时间
     * 操作时间范围查询的起始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     * 操作时间范围查询的结束时间
     */
    private LocalDateTime endTime;
}