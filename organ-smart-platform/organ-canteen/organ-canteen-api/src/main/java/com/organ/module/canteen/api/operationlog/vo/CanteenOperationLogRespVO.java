package com.organ.module.canteen.api.operationlog.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 操作日志信息响应对象
 */
@Data
public class CanteenOperationLogRespVO {
    
    /**
     * 日志记录ID
     */
    private Long id;
    
    /**
     * 日志类型（所属业务模块）
     * 如：用户管理、食堂管理、餐次管理等
     */
    private String logModule;

    /**
     * 操作类型
     * 如：登录、新增、修改、删除等
     */
    private String operationType;
    
    /**
     * 操作内容
     * 详细描述具体的操作行为
     */
    private String operationContent;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 请求数据
     * JSON格式存储请求参数
     */
    private String requestJson;

    /**
     * 响应数据
     * JSON格式存储响应数据
     */
    private String responseJson;
    
    
    /**
     * 操作人
     * 执行此次操作的用户标识
     */
    private String operator;
    
    /**
     * 操作时间
     * 操作发生的具体时间点
     */
    private LocalDateTime operationTime;
    
    /**
     * 数据创建人
     */
    private String creator;
    
    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 数据更新人
     */
    private String updater;
    
    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;
}