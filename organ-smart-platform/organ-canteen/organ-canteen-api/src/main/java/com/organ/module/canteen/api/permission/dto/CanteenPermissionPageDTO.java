package com.organ.module.canteen.api.permission.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.hainancrc.framework.common.pojo.PageParam;

import java.time.LocalDateTime;

/**
 * 权限信息分页 DTO
 */
@ApiModel("权限信息分页 DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CanteenPermissionPageDTO extends PageParam {

    /**
     * 权限名称
     */
    @ApiModelProperty(value = "权限名称", example = "用户管理")
    private String permissionName;
    
    /**
     * 权限编码
     */
    @ApiModelProperty(value = "权限编码", example = "user:manage")
    private String permissionCode;
    
    /**
     * 权限类型
     */
    @ApiModelProperty(value = "权限类型", example = "MENU", notes = "MENU-菜单 BUTTON-按钮 API-接口")
    private String permissionType;
    
    /**
     * 父权限ID
     */
    @ApiModelProperty(value = "父权限ID", example = "1")
    private Long parentId;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    private String status;
    
    /**
     * 创建时间范围
     */
    @ApiModelProperty(value = "创建时间范围")
    private LocalDateTime[] createTime;

}