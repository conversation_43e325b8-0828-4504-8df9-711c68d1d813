package com.organ.module.canteen.api.canteenuser.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 食堂用户信息响应对象
 */
@Data
public class CanteenUserRespVO {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户姓名
     */
    private String username;
    
    /**
     * 用户手机号
     */
    private String mobile;
    
    /**
     * 用户所属单位/部门
     */
    private String dept;
    
    /**
     * 就餐食堂ID
     */
    private Long canteenId;
    
    /**
     * 就餐食堂编码
     */
    private String canteenCode;
    
    /**
     * 用户卡里余额（预留）
     */
    private BigDecimal balance;
    
    /**
     * 用户持卡类别
     * 1-一类、2-二类、3-三类
     */
    private String cardType;
    
    /**
     * 用户类型
     * REGULAR-编内、CONTRACT-编外、FAMILY-家属
     */
    private String userType;
    
    /**
     * 卡开通时间
     */
    private LocalDateTime cardOpenTime;
    
    /**
     * 卡过期时间（不设置时永久有效）
     */
    private LocalDateTime cardExpireTime;
    
    /**
     * 是否允许跨区域用餐
     * 0-不允许、1-允许
     */
    private Integer crossRegionFlag;
    
    /**
     * 就餐区域
     */
    private String diningRegion;
    
    /**
     * 用户状态
     * ACTIVE-正常、DISABLED-禁用
     */
    private String status;
    
    /**
     * 数据创建人
     */
    private String creator;
    
    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 数据更新人
     */
    private String updater;
    
    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;
}