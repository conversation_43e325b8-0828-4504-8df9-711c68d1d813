package com.organ.module.auth.api;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 智慧食堂用户信息VO
 *
 * <AUTHOR>
 */
@ApiModel("智慧食堂用户信息")
@Data
public class UserInfoData {

    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户姓名
     */
    private String username;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户所属单位
     */
    private String dept;

    /**
     * 就餐食堂id（原开卡食堂，关联食堂信息表）
     */
    private Long canteenId;

    /**
     * 用户持卡类别（1-一类 2-二类 3-三类）
     */
    private String cardType;

    /**
     * 用户类型（REGULAR-编内 CONTRACT-编外）
     */
    private String userType;

    /**
     * 卡过期时间（不设置时就是永久有效）
     */
    private LocalDateTime cardExpireTime;

    /**
     * 是否允许跨区域用餐（0-不允许 1-允许）
     */
    private Boolean crossRegionFlag;

    /**
     * 就餐区域（关联字典表 字典 dining_region）
     */
    private String diningRegion;

    /**
     * 用户状态（ACTIVE-正常 DISABLED-禁用）
     */
    private String status;
}
