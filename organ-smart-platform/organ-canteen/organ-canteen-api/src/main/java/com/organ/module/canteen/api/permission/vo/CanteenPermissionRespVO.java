package com.organ.module.canteen.api.permission.vo;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 权限信息响应 VO
 */
@ApiModel("权限信息响应 VO")
@Data
public class CanteenPermissionRespVO {

    /**
     * 权限ID
     */
    @ApiModelProperty(value = "权限ID", example = "1")
    private Long id;
    
    /**
     * 权限名称
     */
    @ApiModelProperty(value = "权限名称", example = "用户管理")
    private String permissionName;
    
    /**
     * 权限编码
     */
    @ApiModelProperty(value = "权限编码", example = "user:manage")
    private String permissionCode;
    
    /**
     * 权限类型
     */
    @ApiModelProperty(value = "权限类型", example = "MENU", notes = "MENU-菜单 BUTTON-按钮 API-接口")
    private String permissionType;
    
    /**
     * 父权限ID
     */
    @ApiModelProperty(value = "父权限ID", example = "1")
    private Long parentId;
    
    /**
     * 路由路径
     */
    @ApiModelProperty(value = "路由路径", example = "/user/manage")
    private String path;
    
    /**
     * 组件路径
     */
    @ApiModelProperty(value = "组件路径", example = "user/UserManage")
    private String component;
    
    /**
     * 图标
     */
    @ApiModelProperty(value = "图标", example = "user")
    private String icon;
    
    /**
     * 排序序号
     */
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer sortOrder;
    
    /**
     * 是否显示
     */
    @ApiModelProperty(value = "是否显示", example = "1", notes = "0-隐藏 1-显示")
    private Integer visible;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    private String status;
    
    /**
     * 权限描述
     */
    @ApiModelProperty(value = "权限描述", example = "用户信息管理功能")
    private String description;
    
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "admin")
    private String creator;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updater;
    
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}