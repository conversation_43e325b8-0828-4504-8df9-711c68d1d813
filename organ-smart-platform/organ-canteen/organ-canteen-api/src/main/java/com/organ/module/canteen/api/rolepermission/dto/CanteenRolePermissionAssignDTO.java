package com.organ.module.canteen.api.rolepermission.dto;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 角色权限分配 DTO
 */
@ApiModel("角色权限分配 DTO")
@Data
public class CanteenRolePermissionAssignDTO {

    /**
     * 角色ID
     */
    @ApiModelProperty(value = "角色ID", required = true, example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;
    
    /**
     * 权限ID集合
     */
    @ApiModelProperty(value = "权限ID集合", required = true)
    @NotEmpty(message = "权限ID集合不能为空")
    private Set<Long> permissionIds;

}