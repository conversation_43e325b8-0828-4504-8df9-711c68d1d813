package com.organ.module.canteen.api.announcement.dto;

import com.hainancrc.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公告分页查询参数
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanteenAnnouncementPageDTO extends PageParam {

    /**
     * 公告标题
     */
    private String title;

    /**
     * 状态（DRAFT-草稿 PUBLISHED-已发布 OFFLINE-已下线）
     */
    private String status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间  
     */
    private String endTime;
}