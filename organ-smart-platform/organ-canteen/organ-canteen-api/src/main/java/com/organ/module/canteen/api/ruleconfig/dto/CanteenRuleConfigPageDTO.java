package com.organ.module.canteen.api.ruleconfig.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.hainancrc.framework.common.pojo.PageParam;

import java.time.LocalDateTime;

/**
 * 系统规则配置分页 DTO
 */
@ApiModel("系统规则配置分页 DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CanteenRuleConfigPageDTO extends PageParam {

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称", example = "预约限制")
    private String ruleName;
    
    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码", example = "BOOKING_LIMIT")
    private String ruleCode;
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "ACTIVE", notes = "ACTIVE-启用 DISABLED-禁用")
    private String status;
    
    /**
     * 创建时间范围
     */
    @ApiModelProperty(value = "创建时间范围")
    private LocalDateTime[] createTime;

}