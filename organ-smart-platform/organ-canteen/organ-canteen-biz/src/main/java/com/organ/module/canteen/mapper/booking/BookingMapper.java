package com.organ.module.canteen.mapper.booking;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.organ.module.canteen.api.booking.dto.BookingPageDTO;
import com.organ.module.canteen.api.booking.vo.BookingExportVO;
import com.organ.module.canteen.entity.BookingDO;
import com.organ.module.canteen.api.booking.vo.BookingPageRespVO;
import com.organ.module.canteen.api.booking.vo.BookingRespVO;
import com.organ.module.enums.BookingStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BookingMapper extends BaseMapperX<BookingDO> {

    Page<BookingPageRespVO> selectBookingPage(@Param("page") Page<BookingPageRespVO> page, @Param("dto") BookingPageDTO dto);

    BookingRespVO selectBookingById(Long bookingId);

    List<BookingExportVO> selectBookingExcelList(@Param("dto") BookingPageDTO pageDTO);

    default void manualWriteOff(Long bookingId) {
        update(null, new LambdaUpdateWrapper<BookingDO>()
                .eq(BookingDO::getId, bookingId)
                .set(BookingDO::getBookingStatus, BookingStatusEnum.VERIFIED));
    }

    default int cancel(Long id) {
        return update(null, new LambdaUpdateWrapper<BookingDO>()
                .eq(BookingDO::getId, id)
                .set(BookingDO::getBookingStatus, BookingStatusEnum.CANCELED));
    }
}
