package com.organ.module.canteen.service.ruleconfig;

import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigCreateDTO;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigPageDTO;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigUpdateDTO;
import com.organ.module.canteen.api.ruleconfig.vo.CanteenRuleConfigRespVO;

import java.util.List;

/**
 * 系统规则配置服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface CanteenRuleConfigService {

    /**
     * 创建规则配置
     *
     * @param createDTO 创建参数
     * @return 规则配置ID
     */
    Long createRuleConfig(CanteenRuleConfigCreateDTO createDTO);

    /**
     * 更新规则配置
     *
     * @param updateDTO 更新参数
     */
    void updateRuleConfig(CanteenRuleConfigUpdateDTO updateDTO);

    /**
     * 删除规则配置
     *
     * @param id 规则配置ID
     */
    void deleteRuleConfig(Long id);

    /**
     * 根据ID获取规则配置
     *
     * @param id 规则配置ID
     * @return 规则配置信息
     */
    CanteenRuleConfigRespVO getRuleConfig(Long id);

    /**
     * 分页查询规则配置
     *
     * @param pageDTO 查询参数
     * @return 分页结果
     */
    PageResult<CanteenRuleConfigRespVO> getRuleConfigPage(CanteenRuleConfigPageDTO pageDTO);

    /**
     * 启用规则配置
     *
     * @param id 规则配置ID
     */
    void enableRuleConfig(Long id);

    /**
     * 禁用规则配置
     *
     * @param id 规则配置ID
     */
    void disableRuleConfig(Long id);

    /**
     * 获取所有规则配置列表
     *
     * @return 规则配置列表
     */
    List<CanteenRuleConfigRespVO> getRuleConfigList();

    /**
     * 根据规则编码获取规则配置
     *
     * @param ruleCode 规则编码
     * @return 规则配置信息
     */
    CanteenRuleConfigRespVO getRuleConfigByCode(String ruleCode);
}