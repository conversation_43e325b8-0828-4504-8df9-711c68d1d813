package com.organ.module.auth.service.hztlogin.impl;

import cn.hutool.json.JSONUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.redis.service.RedisService;
import com.organ.module.auth.api.dto.AuthLoginDTO;
import com.organ.module.auth.api.dto.HztUserInfoDTO;
import com.organ.module.auth.api.dto.ThirdPartyTokenDTO;
import com.organ.module.auth.api.vo.AuthUserInfoVO;
import com.organ.module.auth.config.hztlogin.HztAuthLoginConfig;
import com.organ.module.auth.service.hztlogin.HztAuthLoginService;
import com.organ.module.canteen.api.canteenuser.vo.CanteenUserRespVO;
import com.organ.module.canteen.convert.canteenuser.CanteenUserConvert;
import com.organ.module.canteen.entity.CanteenUserDO;
import com.organ.module.canteen.mapper.canteenuser.CanteenUserMapper;
import com.organ.module.common.utils.HztApiUtils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 第三方登录服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class HztAuthLoginServiceImpl implements HztAuthLoginService {

    @Resource
    private HztAuthLoginConfig thirdPartyAuthConfig;

    @Resource
    private RedisService redisService;

    @Resource
    private CanteenUserMapper canteenUserMapper;

    /**
     * Redis中存储用户信息的key前缀
     */
    private static final String USER_CACHE_KEY_PREFIX = "canteenRk-";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuthUserInfoVO loginByAccessToken(AuthLoginDTO loginDTO) {
        log.info("开始第三方登录，accessToken: {}, clientType: {}", loginDTO.getAccessToken(), loginDTO.getClientType());

        try {

            // 1. 获取 HZT Token
            ThirdPartyTokenDTO thirdPartyToken = HztApiUtils.getTokenByAccessCode(loginDTO.getAccessToken(),
                    loginDTO.getClientType(), thirdPartyAuthConfig);
            // redis 存储的key
            String cacheKey = USER_CACHE_KEY_PREFIX + thirdPartyToken.getAccessToken();
            // 存储token对应的客户端
            redisService.setCacheObject(cacheKey + ":clientType", loginDTO.getClientType(),
                    thirdPartyToken.getExpiredIn() * 2, TimeUnit.SECONDS);

            // 2. 获取 HZT 用户信息
            CanteenUserDO canteenUserInfo = getUserInfoByToken(thirdPartyToken.getAccessToken(),
                    loginDTO.getClientType(), true, thirdPartyToken.getExpiredIn());

            // 5. 构建返回结果
            AuthUserInfoVO loginVO = new AuthUserInfoVO();
            loginVO.setToken(thirdPartyToken.getAccessToken());
            loginVO.setRefreshToken(thirdPartyToken.getRefreshToken());
            loginVO.setUserInfo(CanteenUserConvert.INSTANCE.convertToLoginUserInfo(canteenUserInfo));
            loginVO.setExpiresIn(thirdPartyToken.getExpiredIn());

            log.info("第三方登录成功，用户ID: {}, token: {}", canteenUserInfo.getId(), thirdPartyToken);
            return loginVO;

        } catch (ServiceException e) {
            log.error("第三方登录失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("第三方登录异常", e);
            throw new ServiceException(500, "登录失败: " + e.getMessage());
        }
    }

    /**
     * 通过token获取用户的信息
     * 
     * @param token token
     * @return 用户信息
     * @throws ServiceException 如果用户未登录或token已过期
     */
    @Override
    @Transactional
    public CanteenUserDO getUserInfoByToken(String token, String clientType, Boolean cache, Long expiredIn) {
        try {
            // 调用 HTZ API 获取用户信息
            HztUserInfoDTO hztUserInfo = HztApiUtils.getUserInfoByToken(token, clientType, thirdPartyAuthConfig);

            // 通过匹配措施，将hztUserInfo对应到当前智慧食堂的用户信息
            CanteenUserDO canteenUserInfo = hztUserConvertToCanteenUser(hztUserInfo);

            if (canteenUserInfo == null) {
                throw new ServiceException(401, "用户信息不存在，请重新登录");
            }

            // redis 存储的key
            String cacheKey = USER_CACHE_KEY_PREFIX + token;

            // 过期时间
            Long expiredTime = expiredIn != null ? expiredIn : 30 * 60L;

            // 存储用户信息
            redisService.setCacheObject(cacheKey, JSONUtil.toJsonStr(canteenUserInfo),
                    expiredTime, TimeUnit.SECONDS);

            log.info("用户信息已存储到Redis，cacheKey: {}, expiredIn: {}", cacheKey, expiredTime);

            return canteenUserInfo;
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            throw new ServiceException(500, "获取用户信息失败: " + e.getMessage());
        }

    }

    /**
     * 匹配用户信息
     * 
     * @param hztUserInfo 第三方用户信息
     * @return 智慧食堂用户信息
     */
    private CanteenUserDO hztUserConvertToCanteenUser(HztUserInfoDTO hztUserInfo) {
        // 构建mybatis-plus的查询条件 未删除 + （用户名 或者 手机号 或者 第三方用户ID 匹配）
        QueryWrapper<CanteenUserDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0)
                .and(i -> i.eq("username", hztUserInfo.getNickName())
                        .or().eq("mobile", hztUserInfo.getPhone())
                        .or().eq("id", hztUserInfo.getExternalId()));
        // 匹配用户名或者手机号的食堂用户账号
        List<CanteenUserDO> canteenUserList = canteenUserMapper.selectList(queryWrapper);

        // 未匹配用户
        if (canteenUserList.size() == 0) {
            log.warn("未匹配到用户，用户名: {}, 手机号: {}, 第三方用户ID: {}", hztUserInfo.getNickName(), hztUserInfo.getPhone(),
                    hztUserInfo.getExternalId());
            return null;
        }

        // 匹配 用户名+手机号 的用户
        List<CanteenUserDO> matchUsers = canteenUserList.stream().map(cUer -> {
            if (hztUserInfo.getNickName().equals(cUer.getUsername())
                    && hztUserInfo.getPhone().equals(cUer.getMobile())) {
                return cUer;
            }
            return null;
        }).collect(Collectors.toList());

        if (matchUsers.size() > 1) {
            log.warn("匹配到多个用户，用户名: {}, 手机号: {}, 第三方用户ID: {}", hztUserInfo.getNickName(), hztUserInfo.getPhone(),
                    hztUserInfo.getExternalId());
            // return null;
        }
        // 匹配到的用户
        CanteenUserDO matchUser = matchUsers.get(0);

        return matchUser;
    }

}
