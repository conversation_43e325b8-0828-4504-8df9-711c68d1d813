package com.organ.module.canteen.service.canteenuser;

import java.util.*;
import javax.validation.*;

import com.organ.module.canteen.api.canteenuser.dto.*;
import com.organ.module.canteen.api.canteenuser.vo.*;
import com.organ.module.canteen.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 食堂用户信息 Service 接口
 */
public interface CanteenUserService {
    
    /**
     * 创建新的食堂用户
     * 
     * 验证用户信息的合法性，包括必填字段校验、食堂编码有效性等，
     * 然后将用户信息保存到数据库中
     *
     * @param createDTO 用户创建请求对象，包含用户基本信息
     * @return 新创建用户的ID
     */
    Long create(@Valid CanteenUserCreateDTO createDTO);
    
    /**
     * 更新食堂用户信息
     * 
     * 根据用户ID更新用户的基本信息，支持部分字段更新
     *
     * @param updateDTO 用户更新请求对象，包含需要更新的用户信息
     */
    void update(@Valid CanteenUserUpdateDTO updateDTO);
    
    /**
     * 获取所有食堂用户列表
     * 
     * 查询数据库中所有未删除的用户信息
     *
     * @return 用户信息列表
     */
    List<CanteenUserDO> getList();
    
    /**
     * 分页查询食堂用户信息
     * 
     * 支持按用户名、手机号、部门、食堂等条件进行筛选查询
     *
     * @param pageDTO 分页查询条件，包含页码、页大小及筛选条件
     * @return 分页用户信息结果
     */
    PageResult<CanteenUserDO> getPage(CanteenUserPageDTO pageDTO);
    
    /**
     * 删除食堂用户
     * 
     * 执行逻辑删除，将deleted字段设置为1，不物理删除数据
     *
     * @param id 要删除的用户ID
     */
    void delete(Long id);
    
    /**
     * 根据ID获取用户详细信息
     * 
     * 查询指定ID的用户完整信息，包括所有字段
     *
     * @param id 用户ID
     * @return 用户详细信息，如果用户不存在则返回null
     */
    CanteenUserDO get(Long id);
}