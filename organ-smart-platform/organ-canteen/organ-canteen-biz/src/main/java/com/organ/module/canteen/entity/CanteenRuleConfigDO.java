package com.organ.module.canteen.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 系统规则配置 DO
 */
@TableName("canteen_rule_config")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CanteenRuleConfigDO extends BaseDO {

    /**
     * 规则配置ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 规则编码
     */
    private String ruleCode;
    
    /**
     * 规则阈值
     */
    private Integer ruleValue;
    
    /**
     * 时间单位
     * DAY-天 WEEK-周 MONTH-月
     */
    private String timeUnit;
    
    /**
     * 规则描述
     */
    private String ruleDescription;
    
    /**
     * 状态
     * ACTIVE-启用 DISABLED-禁用
     */
    private String status;

}