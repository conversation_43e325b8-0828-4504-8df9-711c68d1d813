package com.organ.module.canteen.controller.booking.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.booking.dto.BookingCreateDTO;
import com.organ.module.canteen.api.booking.dto.BookingPageDTO;
import com.organ.module.canteen.api.booking.vo.ReserveRespVO;
import com.organ.module.canteen.service.booking.BookingService;
import com.organ.module.canteen.api.booking.vo.BookingPageRespVO;
import com.organ.module.canteen.api.booking.vo.BookingRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "预约信息")
@RestController
@RequestMapping("/booking")
public class BookingController {

    @Resource
    private BookingService bookingService;

    @GetMapping("/page")
    @ApiOperation(value = "核销情况分页查询")
    public CommonResult<PageResult<BookingPageRespVO>> getBookingPage(@Valid BookingPageDTO pageDTO) {
        return success(bookingService.selectBookingPage(pageDTO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获取预约详情")
    public CommonResult<BookingRespVO> getBookingById(@RequestParam Long id) {
        return success(bookingService.selectBookingById(id));
    }

    @PostMapping("/reserve")
    @ApiOperation(value = "预约")
    public CommonResult<ReserveRespVO> reserve(@Valid @RequestBody BookingCreateDTO createDTO) {
        return success(bookingService.reserve(createDTO));
    }

    @PostMapping("/cancel")
    @ApiOperation(value = "取消预约")
    public CommonResult<Boolean> cancel(@RequestParam Long id) {
        return success(bookingService.cancel(id));
    }

    @PostMapping("/manual-write-off")
    @ApiOperation(value = "手动核销")
    public CommonResult<Boolean> manualWriteOff(@RequestParam Long id) {
        return success(bookingService.manualWriteOff(id));
    }

    @GetMapping("/export")
    @ApiOperation(value = "导出核销情况信息")
    public void exportBooking(HttpServletResponse response, BookingPageDTO pageDTO) {
        bookingService.export(response, pageDTO);
    }

}
