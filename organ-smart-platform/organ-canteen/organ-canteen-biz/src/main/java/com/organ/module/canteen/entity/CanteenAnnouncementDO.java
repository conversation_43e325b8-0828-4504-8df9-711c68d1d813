package com.organ.module.canteen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 公告信息实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("canteen_announcement")
public class CanteenAnnouncementDO extends BaseDO {

    /**
     * 公告id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公告标题
     */
    @TableField("title")
    private String title;

    /**
     * 公告内容
     */
    @TableField("content")
    private String content;

    /**
     * 状态（DRAFT-草稿 PUBLISHED-已发布 OFFLINE-已下线）
     */
    @TableField("status")
    private String status;

    /**
     * 发布时间
     */
    @TableField("publish_time")
    private LocalDateTime publishTime;
}