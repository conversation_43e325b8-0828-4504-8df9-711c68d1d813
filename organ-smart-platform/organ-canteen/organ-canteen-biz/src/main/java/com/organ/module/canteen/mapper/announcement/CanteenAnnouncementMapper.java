package com.organ.module.canteen.mapper.announcement;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementPageDTO;
import com.organ.module.canteen.entity.CanteenAnnouncementDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

/**
 * 公告信息Mapper
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper
public interface CanteenAnnouncementMapper extends BaseMapper<CanteenAnnouncementDO> {

    /**
     * 分页查询公告
     *
     * @param page 分页参数
     * @param pageDTO 查询条件
     * @return 分页结果
     */
    default IPage<CanteenAnnouncementDO> selectPage(Page<CanteenAnnouncementDO> page, CanteenAnnouncementPageDTO pageDTO) {
        QueryWrapper<CanteenAnnouncementDO> wrapper = new QueryWrapper<>();
        
        // 根据标题模糊查询
        if (StringUtils.hasText(pageDTO.getTitle())) {
            wrapper.like("title", pageDTO.getTitle());
        }
        
        // 根据状态查询
        if (StringUtils.hasText(pageDTO.getStatus())) {
            wrapper.eq("status", pageDTO.getStatus());
        }
        
        // 根据创建人查询
        if (StringUtils.hasText(pageDTO.getCreator())) {
            wrapper.like("creator", pageDTO.getCreator());
        }
        
        // 根据时间范围查询
        if (StringUtils.hasText(pageDTO.getStartTime())) {
            wrapper.ge("create_time", pageDTO.getStartTime());
        }
        if (StringUtils.hasText(pageDTO.getEndTime())) {
            wrapper.le("create_time", pageDTO.getEndTime());
        }
        
        // 按创建时间倒序
        wrapper.orderByDesc("create_time");
        
        // 排除已删除数据
        wrapper.eq("deleted", 0);
        
        return selectPage(page, wrapper);
    }
}