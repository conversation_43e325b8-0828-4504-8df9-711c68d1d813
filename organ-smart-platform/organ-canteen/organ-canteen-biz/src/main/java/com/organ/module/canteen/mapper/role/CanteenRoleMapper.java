package com.organ.module.canteen.mapper.role;

import com.organ.module.canteen.entity.CanteenRoleDO;
import com.organ.module.canteen.api.role.dto.CanteenRolePageDTO;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 角色信息 Mapper 接口
 */
@Mapper
public interface CanteenRoleMapper extends BaseMapperX<CanteenRoleDO> {

    /**
     * 查询所有角色列表
     * 
     * @return 角色信息列表
     */
    default List<CanteenRoleDO> selectList() {
        return selectList(new LambdaQueryWrapperX<CanteenRoleDO>()
                .orderByDesc(CanteenRoleDO::getId));
    }
    
    /**
     * 分页查询角色信息
     * 支持按角色名称、角色编码、状态等条件进行筛选
     * 
     * @param pageDTO 分页查询条件
     * @return 分页角色信息结果
     */
    default PageResult<CanteenRoleDO> selectPage(CanteenRolePageDTO pageDTO) {
        return selectPage(pageDTO, new LambdaQueryWrapperX<CanteenRoleDO>()
                .likeIfPresent(CanteenRoleDO::getRoleName, pageDTO.getRoleName())
                .likeIfPresent(CanteenRoleDO::getRoleCode, pageDTO.getRoleCode())
                .eqIfPresent(CanteenRoleDO::getStatus, pageDTO.getStatus())
                .orderByDesc(CanteenRoleDO::getId));
    }
}