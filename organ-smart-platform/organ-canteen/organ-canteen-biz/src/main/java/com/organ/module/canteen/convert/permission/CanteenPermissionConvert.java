package com.organ.module.canteen.convert.permission;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.permission.dto.*;
import com.organ.module.canteen.api.permission.vo.*;
import com.organ.module.canteen.entity.*;
import java.util.List;

/**
 * 权限信息对象转换器
 */
@Mapper
public interface CanteenPermissionConvert {
    CanteenPermissionConvert INSTANCE = Mappers.getMapper(CanteenPermissionConvert.class);

    /**
     * 将创建请求对象转换为数据库实体对象
     * 
     * @param bean 创建请求对象
     * @return 权限数据库实体对象
     */
    CanteenPermissionDO convert(CanteenPermissionCreateDTO bean);
    
    /**
     * 将更新请求对象转换为数据库实体对象
     * 
     * @param bean 更新请求对象
     * @return 权限数据库实体对象
     */
    CanteenPermissionDO convert(CanteenPermissionUpdateDTO bean);
    
    /**
     * 将数据库实体对象转换为响应对象
     * 
     * @param bean 数据库实体对象
     * @return 权限信息响应对象
     */
    CanteenPermissionRespVO convert(CanteenPermissionDO bean);
    
    /**
     * 将数据库实体对象列表转换为响应对象列表
     * 
     * @param list 数据库实体对象列表
     * @return 权限信息响应对象列表
     */
    List<CanteenPermissionRespVO> convertList(List<CanteenPermissionDO> list);
    
    /**
     * 将数据库实体对象分页结果转换为响应对象分页结果
     * 
     * @param page 数据库实体对象分页结果
     * @return 权限信息响应对象分页结果
     */
    PageResult<CanteenPermissionRespVO> convertPage(PageResult<CanteenPermissionDO> page);
}