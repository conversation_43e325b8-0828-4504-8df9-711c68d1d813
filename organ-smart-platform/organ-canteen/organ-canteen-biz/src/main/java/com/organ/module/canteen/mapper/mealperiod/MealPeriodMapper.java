package com.organ.module.canteen.mapper.mealperiod;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.organ.module.canteen.entity.MealPeriodDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface MealPeriodMapper extends BaseMapperX<MealPeriodDO> {

    /**
     * 获取食堂的餐次信息
     *
     * @param canteenId canteenId
     * @return result
     */
    default List<MealPeriodDO> getMealPeriodByCanteenId(Long canteenId) {
        return selectList(new LambdaQueryWrapper<MealPeriodDO>()
                .eq(MealPeriodDO::getCanteenId, canteenId)
                .orderByAsc(MealPeriodDO::getStartTime)
        );
    }

    /**
     * 获取可用的餐次
     *
     * @param canteenId canteenId
     * @return result
     */
    default List<MealPeriodDO> getAvailableMealPeriodByCanteenId(Long canteenId) {
        return selectList(new LambdaQueryWrapper<MealPeriodDO>()
                .eq(MealPeriodDO::getCanteenId, canteenId)
                .eq(MealPeriodDO::getStatus, "AVAILABLE")
                .eq(MealPeriodDO::getBookingAvailableStatus, "OPEN")
        );
    }

}
