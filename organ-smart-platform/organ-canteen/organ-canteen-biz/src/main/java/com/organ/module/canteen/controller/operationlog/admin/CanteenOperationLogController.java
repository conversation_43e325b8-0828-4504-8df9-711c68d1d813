package com.organ.module.canteen.controller.operationlog.admin;

import com.hainancrc.framework.common.pojo.*;
import com.organ.module.canteen.api.operationlog.dto.*;
import com.organ.module.canteen.api.operationlog.vo.*;
import com.organ.module.canteen.convert.operationlog.*;
import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.service.operationlog.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * 操作日志审计管理 Controller
 */
@Api(tags = "操作日志审计")
@RestController
@RequestMapping("/operationLog")
@Validated
public class CanteenOperationLogController {
    
    @Resource
    private CanteenOperationLogService operationLogService;


    /**
     * 获取所有操作日志列表
     * 
     * @return 操作日志信息列表
     */
    @GetMapping("/list")
    @ApiOperation("获得操作日志列表")
    public CommonResult<List<CanteenOperationLogRespVO>> getList() {
        List<CanteenOperationLogDO> list = operationLogService.getList();
        return success(CanteenOperationLogConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页查询操作日志信息
     * 
     * @param pageDTO 分页查询条件，包含操作人、操作类型、时间范围等筛选条件
     * @return 分页操作日志信息
     */
    @GetMapping("/page")
    @ApiOperation("获得操作日志分页")
    public CommonResult<PageResult<CanteenOperationLogRespVO>> getPage(@Valid CanteenOperationLogPageDTO pageDTO) {
        PageResult<CanteenOperationLogDO> pageResult = operationLogService.getPage(pageDTO);
        return success(CanteenOperationLogConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 根据ID获取操作日志详情
     * 
     * @param id 日志ID
     * @return 操作日志详细信息
     */
    @GetMapping("/get")
    @ApiOperation("获得操作日志详情")
    @ApiImplicitParam(name = "id", value = "日志ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<CanteenOperationLogRespVO> get(@RequestParam("id") Long id) {
        CanteenOperationLogDO operationLog = operationLogService.get(id);
        return success(CanteenOperationLogConvert.INSTANCE.convert(operationLog));
    }

    /**
     * 导出操作日志
     * 
     * @param pageDTO 导出条件，支持按时间范围、操作人等条件筛选
     * @return 导出结果提示
     */
    @GetMapping("/export")
    @ApiOperation("导出操作日志")
    public CommonResult<String> export(@Valid CanteenOperationLogPageDTO pageDTO) {
        return success(operationLogService.exportLogs(pageDTO));
    }
}