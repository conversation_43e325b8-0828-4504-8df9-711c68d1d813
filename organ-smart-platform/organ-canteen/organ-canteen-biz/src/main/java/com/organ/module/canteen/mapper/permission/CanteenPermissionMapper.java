package com.organ.module.canteen.mapper.permission;

import com.organ.module.canteen.entity.CanteenPermissionDO;
import com.organ.module.canteen.api.permission.dto.CanteenPermissionPageDTO;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 权限信息 Mapper 接口
 */
@Mapper
public interface CanteenPermissionMapper extends BaseMapperX<CanteenPermissionDO> {

    /**
     * 查询所有权限列表
     * 按排序序号和ID排序
     * 
     * @return 权限信息列表
     */
    default List<CanteenPermissionDO> selectList() {
        return selectList(new LambdaQueryWrapperX<CanteenPermissionDO>()
                .orderByAsc(CanteenPermissionDO::getSortOrder)
                .orderByDesc(CanteenPermissionDO::getId));
    }
    
    /**
     * 分页查询权限信息
     * 支持按权限名称、权限编码、权限类型、状态等条件进行筛选
     * 
     * @param pageDTO 分页查询条件
     * @return 分页权限信息结果
     */
    default PageResult<CanteenPermissionDO> selectPage(CanteenPermissionPageDTO pageDTO) {
        return selectPage(pageDTO, new LambdaQueryWrapperX<CanteenPermissionDO>()
                .likeIfPresent(CanteenPermissionDO::getPermissionName, pageDTO.getPermissionName())
                .likeIfPresent(CanteenPermissionDO::getPermissionCode, pageDTO.getPermissionCode())
                .eqIfPresent(CanteenPermissionDO::getPermissionType, pageDTO.getPermissionType())
                .eqIfPresent(CanteenPermissionDO::getParentId, pageDTO.getParentId())
                .eqIfPresent(CanteenPermissionDO::getStatus, pageDTO.getStatus())
                .orderByAsc(CanteenPermissionDO::getSortOrder)
                .orderByDesc(CanteenPermissionDO::getId));
    }
    
    /**
     * 查询指定父权限下的子权限列表
     * 
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    default List<CanteenPermissionDO> selectListByParentId(Long parentId) {
        return selectList(new LambdaQueryWrapperX<CanteenPermissionDO>()
                .eq(CanteenPermissionDO::getParentId, parentId)
                .orderByAsc(CanteenPermissionDO::getSortOrder)
                .orderByDesc(CanteenPermissionDO::getId));
    }
}