package com.organ.module.canteen.service.rolepermission;

import java.util.*;
import javax.validation.*;

import com.organ.module.canteen.api.rolepermission.dto.*;

/**
 * 角色权限关联 Service 接口
 */
public interface CanteenRolePermissionService {
    
    /**
     * 为角色分配权限
     * 
     * 删除角色原有的所有权限，然后分配新的权限集合
     *
     * @param assignDTO 角色权限分配请求参数
     */
    void assignRolePermissions(@Valid CanteenRolePermissionAssignDTO assignDTO);
    
    /**
     * 根据角色ID获取权限ID列表
     * 
     * 查询指定角色拥有的所有权限ID
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getPermissionIdsByRoleId(Long roleId);
    
    /**
     * 根据权限ID获取角色ID列表
     * 
     * 查询拥有指定权限的所有角色ID
     *
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    List<Long> getRoleIdsByPermissionId(Long permissionId);
    
    /**
     * 根据角色ID列表获取所有权限ID
     * 
     * 查询多个角色拥有的所有权限ID（去重）
     *
     * @param roleIds 角色ID列表
     * @return 权限ID列表
     */
    List<Long> getPermissionIdsByRoleIds(Collection<Long> roleIds);
    
    /**
     * 删除角色的所有权限
     * 
     * 当删除角色时，同时删除该角色的所有权限关联
     *
     * @param roleId 角色ID
     */
    void deleteByRoleId(Long roleId);
    
    /**
     * 删除权限的所有角色关联
     * 
     * 当删除权限时，同时删除该权限的所有角色关联
     *
     * @param permissionId 权限ID
     */
    void deleteByPermissionId(Long permissionId);
    
}