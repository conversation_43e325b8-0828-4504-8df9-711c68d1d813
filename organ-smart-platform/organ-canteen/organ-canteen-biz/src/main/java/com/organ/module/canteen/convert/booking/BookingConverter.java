package com.organ.module.canteen.convert.booking;

import com.organ.module.canteen.api.booking.dto.BookingCreateDTO;
import com.organ.module.canteen.entity.BookingDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BookingConverter {

    BookingConverter INSTANCE = Mappers.getMapper(BookingConverter.class);

    BookingDO convert(BookingCreateDTO createDTO);

}
