package com.organ.module.canteen.convert.operationlog;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.operationlog.dto.*;
import com.organ.module.canteen.api.operationlog.vo.*;
import com.organ.module.canteen.entity.*;
import java.util.List;

/**
 * 操作日志信息对象转换器
 */
@Mapper
public interface CanteenOperationLogConvert {
    CanteenOperationLogConvert INSTANCE = Mappers.getMapper(CanteenOperationLogConvert.class);

    
    /**
     * 将数据库实体对象转换为响应对象
     * 
     * @param bean 数据库实体对象
     * @return 操作日志信息响应对象
     */
    CanteenOperationLogRespVO convert(CanteenOperationLogDO bean);
    
    /**
     * 将数据库实体对象列表转换为响应对象列表
     * 
     * @param list 数据库实体对象列表
     * @return 操作日志信息响应对象列表
     */
    List<CanteenOperationLogRespVO> convertList(List<CanteenOperationLogDO> list);
    
    /**
     * 将数据库实体对象分页结果转换为响应对象分页结果
     * 
     * @param page 数据库实体对象分页结果
     * @return 操作日志信息响应对象分页结果
     */
    PageResult<CanteenOperationLogRespVO> convertPage(PageResult<CanteenOperationLogDO> page);
}