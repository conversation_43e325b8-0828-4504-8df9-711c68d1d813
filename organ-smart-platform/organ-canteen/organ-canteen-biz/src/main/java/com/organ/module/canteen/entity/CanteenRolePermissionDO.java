package com.organ.module.canteen.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * 角色权限关联 DO
 */
@TableName("canteen_role_permission")
@Data
@EqualsAndHashCode
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CanteenRolePermissionDO {

    /**
     * 关联ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 权限ID
     */
    private Long permissionId;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 删除标识
     * 0-未删除 1-已删除
     */
    @TableLogic
    private Integer deleted;

}