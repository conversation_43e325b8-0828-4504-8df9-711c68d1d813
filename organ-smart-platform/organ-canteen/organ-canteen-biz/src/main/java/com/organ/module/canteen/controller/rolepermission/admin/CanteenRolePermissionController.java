package com.organ.module.canteen.controller.rolepermission.admin;

import com.hainancrc.framework.common.pojo.*;
import com.organ.module.canteen.api.rolepermission.dto.*;
import com.organ.module.canteen.service.rolepermission.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * 角色权限关联管理 Controller
 */
@Api(tags = "角色权限管理")
@RestController
@RequestMapping("/rolePermission")
@Validated
public class CanteenRolePermissionController {
    
    @Resource
    private CanteenRolePermissionService rolePermissionService;

    /**
     * 为角色分配权限
     * 
     * @param assignDTO 角色权限分配请求参数
     * @return 操作结果
     */
    @PostMapping("/assign")
    @ApiOperation("为角色分配权限")
    public CommonResult<Boolean> assignRolePermissions(@Valid @RequestBody CanteenRolePermissionAssignDTO assignDTO) {
        rolePermissionService.assignRolePermissions(assignDTO);
        return success(true);
    }

    /**
     * 根据角色ID获取权限ID列表
     * 
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @GetMapping("/getPermissionsByRole")
    @ApiOperation("根据角色获取权限列表")
    @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<List<Long>> getPermissionIdsByRoleId(@RequestParam("roleId") Long roleId) {
        List<Long> permissionIds = rolePermissionService.getPermissionIdsByRoleId(roleId);
        return success(permissionIds);
    }

    /**
     * 根据权限ID获取角色ID列表
     * 
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    @GetMapping("/getRolesByPermission")
    @ApiOperation("根据权限获取角色列表")
    @ApiImplicitParam(name = "permissionId", value = "权限ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<List<Long>> getRoleIdsByPermissionId(@RequestParam("permissionId") Long permissionId) {
        List<Long> roleIds = rolePermissionService.getRoleIdsByPermissionId(permissionId);
        return success(roleIds);
    }

}