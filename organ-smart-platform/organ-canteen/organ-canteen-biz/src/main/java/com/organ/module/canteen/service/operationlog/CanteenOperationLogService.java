package com.organ.module.canteen.service.operationlog;

import java.util.*;
import javax.validation.*;

import com.organ.module.canteen.api.operationlog.dto.*;
import com.organ.module.canteen.api.operationlog.vo.*;
import com.organ.module.canteen.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 操作日志审计 Service 接口
 */
public interface CanteenOperationLogService {
    
    
    /**
     * 记录操作日志
     * 
     * 用于记录用户的操作行为，包括请求参数、响应数据、IP地址等信息
     *
     * @param logModule 操作模块
     * @param operationType 操作类型
     * @param operationContent 操作内容描述
     * @param clientIp 客户端IP地址
     * @param userAgent 用户代理信息
     * @param requestJson 请求数据JSON
     * @param responseJson 响应数据JSON
     * @param operator 操作人
     */
    void recordLog(String logModule, String operationType, String operationContent, 
                   String clientIp, String userAgent, String requestJson, String responseJson,
                   String operator);

    /**
     * 获取所有操作日志列表
     * 
     * 查询数据库中所有未删除的操作日志记录
     *
     * @return 操作日志信息列表
     */
    List<CanteenOperationLogDO> getList();
    
    /**
     * 分页查询操作日志信息
     * 
     * 支持按操作人、操作类型、操作时间等条件进行筛选查询
     *
     * @param pageDTO 分页查询条件，包含页码、页大小及筛选条件
     * @return 分页操作日志信息结果
     */
    PageResult<CanteenOperationLogDO> getPage(CanteenOperationLogPageDTO pageDTO);
    
    /**
     * 根据ID获取操作日志详细信息
     * 
     * 查询指定ID的操作日志完整信息
     *
     * @param id 日志ID
     * @return 操作日志详细信息，如果日志不存在则返回null
     */
    CanteenOperationLogDO get(Long id);

    /**
     * 导出操作日志
     * 
     * 根据查询条件导出操作日志数据，支持Excel格式
     *
     * @param pageDTO 导出条件，包含时间范围、操作人等筛选条件
     * @return 导出文件路径或导出结果信息
     */
    String exportLogs(CanteenOperationLogPageDTO pageDTO);
}