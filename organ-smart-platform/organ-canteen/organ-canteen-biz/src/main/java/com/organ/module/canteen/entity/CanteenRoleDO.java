package com.organ.module.canteen.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 角色信息 DO
 */
@TableName("canteen_role")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CanteenRoleDO extends BaseDO {

    /**
     * 角色ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 角色编码
     */
    private String roleCode;
    
    /**
     * 角色描述
     */
    private String description;
    
    /**
     * 用户数量
     */
    private Integer userCount;
    
    /**
     * 状态
     * ACTIVE-启用 DISABLED-禁用
     */
    private String status;

}