package com.organ.module.canteen.service.department;

import java.util.*;
import javax.validation.*;

import com.organ.module.canteen.api.department.dto.*;
import com.organ.module.canteen.api.department.vo.*;
import com.organ.module.canteen.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 部门信息 Service 接口
 */
public interface CanteenDepartmentService {
    
    /**
     * 创建新的部门
     * 
     * 验证部门信息的合法性，包括必填字段校验、部门编码唯一性等，
     * 然后将部门信息保存到数据库中
     *
     * @param createDTO 部门创建请求对象，包含部门基本信息
     * @return 新创建部门的ID
     */
    Long create(@Valid CanteenDepartmentCreateDTO createDTO);
    
    /**
     * 更新部门信息
     * 
     * 根据部门ID更新部门的基本信息，支持部分字段更新
     *
     * @param updateDTO 部门更新请求对象，包含需要更新的部门信息
     */
    void update(@Valid CanteenDepartmentUpdateDTO updateDTO);
    
    /**
     * 获取所有部门列表
     * 
     * 查询数据库中所有未删除的部门信息
     *
     * @return 部门信息列表
     */
    List<CanteenDepartmentRespVO> getList();
    
    /**
     * 获取部门树形结构
     * 
     * 构建部门的层级树形结构，包含父子关系
     *
     * @return 部门树形结构列表
     */
    List<CanteenDepartmentRespVO> getDepartmentTree();
    
    /**
     * 分页查询部门信息
     * 
     * 支持按部门名称、部门编码等条件进行筛选查询
     *
     * @param pageDTO 分页查询条件，包含页码、页大小及筛选条件
     * @return 分页部门信息结果
     */
    PageResult<CanteenDepartmentRespVO> getPage(CanteenDepartmentPageDTO pageDTO);
    
    /**
     * 删除部门
     * 
     * 执行逻辑删除，将deleted字段设置为1，不物理删除数据
     * 删除前检查是否存在子部门，如有子部门则不允许删除
     *
     * @param id 要删除的部门ID
     */
    void delete(Long id);
    
    /**
     * 根据ID获取部门详细信息
     * 
     * 查询指定ID的部门完整信息，包括所有字段
     *
     * @param id 部门ID
     * @return 部门详细信息，如果部门不存在则返回null
     */
    CanteenDepartmentRespVO get(Long id);
    
    /**
     * 根据部门编码查询部门信息
     * 
     * @param departmentCode 部门编码
     * @return 部门信息
     */
    CanteenDepartmentRespVO getByCode(String departmentCode);
}