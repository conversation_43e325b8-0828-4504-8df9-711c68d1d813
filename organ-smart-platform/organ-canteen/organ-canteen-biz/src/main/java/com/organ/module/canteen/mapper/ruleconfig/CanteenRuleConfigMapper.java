package com.organ.module.canteen.mapper.ruleconfig;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigPageDTO;
import com.organ.module.canteen.entity.CanteenRuleConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

/**
 * 系统规则配置Mapper
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper
public interface CanteenRuleConfigMapper extends BaseMapper<CanteenRuleConfigDO> {

    /**
     * 分页查询规则配置
     *
     * @param page 分页参数
     * @param pageDTO 查询条件
     * @return 分页结果
     */
    default IPage<CanteenRuleConfigDO> selectPage(Page<CanteenRuleConfigDO> page, CanteenRuleConfigPageDTO pageDTO) {
        QueryWrapper<CanteenRuleConfigDO> wrapper = new QueryWrapper<>();
        
        // 根据规则名称模糊查询
        if (StringUtils.hasText(pageDTO.getRuleName())) {
            wrapper.like("rule_name", pageDTO.getRuleName());
        }
        
        // 根据规则编码模糊查询
        if (StringUtils.hasText(pageDTO.getRuleCode())) {
            wrapper.like("rule_code", pageDTO.getRuleCode());
        }
        
        // 根据状态查询
        if (StringUtils.hasText(pageDTO.getStatus())) {
            wrapper.eq("status", pageDTO.getStatus());
        }
        
        // 根据时间范围查询
        if (pageDTO.getCreateTime() != null && pageDTO.getCreateTime().length == 2) {
            wrapper.between("create_time", pageDTO.getCreateTime()[0], pageDTO.getCreateTime()[1]);
        }
        
        // 按创建时间倒序
        wrapper.orderByDesc("create_time");
        
        // 排除已删除数据
        wrapper.eq("deleted", 0);
        
        return selectPage(page, wrapper);
    }

    /**
     * 根据规则编码查询规则配置
     *
     * @param ruleCode 规则编码
     * @return 规则配置
     */
    default CanteenRuleConfigDO selectByRuleCode(String ruleCode) {
        QueryWrapper<CanteenRuleConfigDO> wrapper = new QueryWrapper<>();
        wrapper.eq("rule_code", ruleCode);
        wrapper.eq("deleted", 0);
        return selectOne(wrapper);
    }
}