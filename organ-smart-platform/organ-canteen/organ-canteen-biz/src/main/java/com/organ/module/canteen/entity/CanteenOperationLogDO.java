package com.organ.module.canteen.entity;

import java.time.LocalDateTime;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 操作日志数据对象
 */
@TableName("canteen_operation_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CanteenOperationLogDO extends BaseDO {

    /**
     * 日志记录id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 日志类型（所属业务模块）
     * 如：用户管理、食堂管理、餐次管理等
     */
    private String logModule;

    /**
     * 操作类型（如：额度调整/手动核销/处罚用户）
     */
    private String operationType;

    /**
     * 操作内容（如：额度调整/手动核销/处罚用户）
     * 详细描述具体的操作行为
     */
    private String operationContent;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 请求数据
     * JSON格式存储请求参数
     */
    private String requestJson;

    /**
     * 响应数据
     * JSON格式存储响应数据
     */
    private String responseJson;


    /**
     * 操作人
     * 执行此次操作的用户标识
     */
    private String operator;

    /**
     * 操作时间
     * 操作发生的具体时间点
     */
    private LocalDateTime operationTime;
}