package com.organ.module.canteen.mapper.canteenuser;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.api.canteenuser.dto.*;

import org.apache.ibatis.annotations.*;

/**
 * 食堂用户信息 Mapper 接口
 */
@Mapper
public interface CanteenUserMapper extends BaseMapperX<CanteenUserDO> {
    
    /**
     * 分页查询用户信息
     * @param reqDTO 分页查询请求对象
     * @return 分页查询结果
     */
    default PageResult<CanteenUserDO> selectPage(CanteenUserPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<CanteenUserDO>()
                .likeIfPresent(CanteenUserDO::getUsername, reqDTO.getUsername())
                .eqIfPresent(CanteenUserDO::getMobile, reqDTO.getMobile())
                .orderByDesc(CanteenUserDO::getUpdateTime));
    }
}