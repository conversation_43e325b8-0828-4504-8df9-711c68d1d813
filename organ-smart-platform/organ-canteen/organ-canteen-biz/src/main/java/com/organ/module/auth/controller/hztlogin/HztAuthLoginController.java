package com.organ.module.auth.controller.hztlogin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.organ.module.auth.api.dto.AuthLoginDTO;
import com.organ.module.auth.api.vo.AuthUserInfoVO;
import com.organ.module.auth.service.hztlogin.HztAuthLoginService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * 第三方登录控制器
 *
 * <AUTHOR>
 */
@Api(tags = "第三方登录")
@RestController
@RequestMapping("/auth")
@Slf4j
public class HztAuthLoginController {

    @Resource
    private HztAuthLoginService authLoginService;

    /**
     * 通过accessToken进行第三方登录
     *
     * @param loginDTO 登录请求参数
     * @return 登录结果
     */
    @PostMapping("/loginByAccessToken")
    @ApiOperation("通过accessToken进行第三方登录")
    public CommonResult<AuthUserInfoVO> loginByAccessToken(@Valid @RequestBody AuthLoginDTO loginDTO) {
        AuthUserInfoVO result = authLoginService.loginByAccessToken(loginDTO);
        return success(result);
    }

}
