package com.organ.module.canteen.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 单位部门 DO
 */
@TableName("canteen_department")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CanteenDepartmentDO extends BaseDO {

    /**
     * 部门ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 部门编码
     */
    private String departmentCode;
    
    /**
     * 父部门ID
     */
    private Long parentId;

}