package com.organ.module.canteen.mapper.reconciliation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.organ.module.canteen.api.reconciliation.dto.ReconciliationPageDTO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationExportVO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationPageRespVO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationStatVO;
import com.organ.module.common.utils.time.TimeRange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ReconciliationMapper {

    ReconciliationStatVO getReconciliationStatByMeal(@Param("canteenId") Long canteenId,
                                                     @Param("mealPeriodId") Long mealPeriodId,
                                                     @Param("mealDate") LocalDateTime mealDate);

    ReconciliationStatVO getReconciliationStatByRange(@Param("canteenId") Long canteenId, @Param("timeRange") TimeRange timeRange);

    Page<ReconciliationPageRespVO> getReconciliationPageByMeal(@Param("page") Page<ReconciliationPageRespVO> page,
                                                               @Param("pageDTO") ReconciliationPageDTO pageDTO,
                                                               @Param("mealPeriodId") Long mealPeriodId,
                                                               @Param("mealDate") LocalDateTime mealDate);

    Page<ReconciliationPageRespVO> getReconciliationPageByRange(@Param("page") Page<ReconciliationPageRespVO> page,
                                                                @Param("pageDTO") ReconciliationPageDTO pageDTO,
                                                                @Param("timeRange") TimeRange timeRange);

    List<ReconciliationExportVO> getReconciliationExportListByMeal(@Param("pageDTO") ReconciliationPageDTO pageDTO,
                                                                   @Param("mealPeriodId") Long mealPeriodId,
                                                                   @Param("mealDate") LocalDateTime localDateTime);

    List<ReconciliationExportVO> getReconciliationExportListByRange(@Param("pageDTO") ReconciliationPageDTO pageDTO,
                                                                    @Param("timeRange") TimeRange timeRange);
}
