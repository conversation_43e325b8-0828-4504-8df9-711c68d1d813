package com.organ.module.canteen.service.role;

import java.util.*;
import javax.validation.*;

import com.organ.module.canteen.api.role.dto.*;
import com.organ.module.canteen.api.role.vo.*;
import com.organ.module.canteen.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 角色信息 Service 接口
 */
public interface CanteenRoleService {
    
    /**
     * 创建角色
     * 
     * 新增一个角色记录，用于系统权限管理
     *
     * @param createDTO 创建角色的请求参数
     * @return 新创建的角色ID
     */
    Long create(@Valid CanteenRoleCreateDTO createDTO);
    
    /**
     * 更新角色
     * 
     * 根据ID更新角色的基本信息
     *
     * @param updateDTO 更新角色的请求参数，包含ID和待更新的字段
     */
    void update(@Valid CanteenRoleUpdateDTO updateDTO);
    
    /**
     * 删除角色
     * 
     * 根据ID逻辑删除角色记录
     *
     * @param id 角色ID
     */
    void delete(Long id);
    
    /**
     * 获取所有角色列表
     * 
     * 查询数据库中所有未删除的角色记录
     *
     * @return 角色信息列表
     */
    List<CanteenRoleDO> getList();
    
    /**
     * 分页查询角色信息
     * 
     * 支持按角色名称、角色编码、状态等条件进行筛选查询
     *
     * @param pageDTO 分页查询条件，包含页码、页大小及筛选条件
     * @return 分页角色信息结果
     */
    PageResult<CanteenRoleDO> getPage(CanteenRolePageDTO pageDTO);
    
    /**
     * 根据ID获取角色详细信息
     * 
     * 查询指定ID的角色完整信息
     *
     * @param id 角色ID
     * @return 角色详细信息，如果角色不存在则返回null
     */
    CanteenRoleDO get(Long id);
    
}