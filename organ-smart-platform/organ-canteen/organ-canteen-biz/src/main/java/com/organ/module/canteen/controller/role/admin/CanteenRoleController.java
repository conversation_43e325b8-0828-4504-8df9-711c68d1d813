package com.organ.module.canteen.controller.role.admin;

import com.hainancrc.framework.common.pojo.*;
import com.organ.module.canteen.api.role.dto.*;
import com.organ.module.canteen.api.role.vo.*;
import com.organ.module.canteen.convert.role.*;
import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.service.role.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * 角色管理 Controller
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/role")
@Validated
public class CanteenRoleController {
    
    @Resource
    private CanteenRoleService roleService;

    /**
     * 创建角色
     * 
     * @param createDTO 创建角色的请求参数
     * @return 新创建的角色ID
     */
    @PostMapping("/create")
    @ApiOperation("创建角色")
    public CommonResult<Long> create(@Valid @RequestBody CanteenRoleCreateDTO createDTO) {
        Long id = roleService.create(createDTO);
        return success(id);
    }

    /**
     * 更新角色
     * 
     * @param updateDTO 更新角色的请求参数
     * @return 操作结果
     */
    @PutMapping("/update")
    @ApiOperation("更新角色")
    public CommonResult<Boolean> update(@Valid @RequestBody CanteenRoleUpdateDTO updateDTO) {
        roleService.update(updateDTO);
        return success(true);
    }

    /**
     * 删除角色
     * 
     * @param id 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    @ApiOperation("删除角色")
    @ApiImplicitParam(name = "id", value = "角色ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        roleService.delete(id);
        return success(true);
    }

    /**
     * 获取所有角色列表
     * 
     * @return 角色信息列表
     */
    @GetMapping("/list")
    @ApiOperation("获得角色列表")
    public CommonResult<List<CanteenRoleRespVO>> getList() {
        List<CanteenRoleDO> list = roleService.getList();
        return success(CanteenRoleConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页查询角色信息
     * 
     * @param pageDTO 分页查询条件，包含角色名称、角色编码、状态等筛选条件
     * @return 分页角色信息
     */
    @GetMapping("/page")
    @ApiOperation("获得角色分页")
    public CommonResult<PageResult<CanteenRoleRespVO>> getPage(@Valid CanteenRolePageDTO pageDTO) {
        PageResult<CanteenRoleDO> pageResult = roleService.getPage(pageDTO);
        return success(CanteenRoleConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 根据ID获取角色详情
     * 
     * @param id 角色ID
     * @return 角色详细信息
     */
    @GetMapping("/get")
    @ApiOperation("获得角色详情")
    @ApiImplicitParam(name = "id", value = "角色ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<CanteenRoleRespVO> get(@RequestParam("id") Long id) {
        CanteenRoleDO role = roleService.get(id);
        return success(CanteenRoleConvert.INSTANCE.convert(role));
    }

}