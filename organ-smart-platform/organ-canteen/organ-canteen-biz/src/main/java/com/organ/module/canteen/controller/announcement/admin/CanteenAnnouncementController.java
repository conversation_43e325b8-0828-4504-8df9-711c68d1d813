package com.organ.module.canteen.controller.announcement.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementCreateDTO;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementPageDTO;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementUpdateDTO;
import com.organ.module.canteen.api.announcement.vo.CanteenAnnouncementRespVO;
import com.organ.module.canteen.service.announcement.CanteenAnnouncementService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 公告信息控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Api(tags = "公告管理")
@Slf4j
@RestController
@RequestMapping("/announcement")
public class CanteenAnnouncementController {

    @Resource
    private CanteenAnnouncementService announcementService;

    @ApiOperation("创建公告")
    @PostMapping("/create")
    public CommonResult<Long> createAnnouncement(@Valid @RequestBody CanteenAnnouncementCreateDTO createDTO) {
        Long id = announcementService.createAnnouncement(createDTO);
        return CommonResult.success(id);
    }

    @ApiOperation("更新公告")
    @PutMapping("/update")
    public CommonResult<Boolean> updateAnnouncement(@Valid @RequestBody CanteenAnnouncementUpdateDTO updateDTO) {
        announcementService.updateAnnouncement(updateDTO);
        return CommonResult.success(true);
    }

    @ApiOperation("删除公告")
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteAnnouncement(@ApiParam("公告ID") @RequestParam("id") Long id) {
        announcementService.deleteAnnouncement(id);
        return CommonResult.success(true);
    }

    @ApiOperation("获取公告详情")
    @GetMapping("/get")
    public CommonResult<CanteenAnnouncementRespVO> getAnnouncement(@ApiParam("公告ID") @RequestParam("id") Long id) {
        CanteenAnnouncementRespVO announcement = announcementService.getAnnouncement(id);
        return CommonResult.success(announcement);
    }

    @ApiOperation("分页查询公告")
    @GetMapping("/page")
    public CommonResult<PageResult<CanteenAnnouncementRespVO>> getAnnouncementPage(@Valid CanteenAnnouncementPageDTO pageDTO) {
        PageResult<CanteenAnnouncementRespVO> pageResult = announcementService.getAnnouncementPage(pageDTO);
        return CommonResult.success(pageResult);
    }

    @ApiOperation("发布公告")
    @PostMapping("/publish")
    public CommonResult<Boolean> publishAnnouncement(@ApiParam("公告ID") @RequestParam("id") Long id) {
        announcementService.publishAnnouncement(id);
        return CommonResult.success(true);
    }

    @ApiOperation("下线公告")
    @PostMapping("/offline")
    public CommonResult<Boolean> offlineAnnouncement(@ApiParam("公告ID") @RequestParam("id") Long id) {
        announcementService.offlineAnnouncement(id);
        return CommonResult.success(true);
    }

    @ApiOperation("获取公告列表")
    @GetMapping("/list")
    public CommonResult<List<CanteenAnnouncementRespVO>> getAnnouncementList() {
        List<CanteenAnnouncementRespVO> announcements = announcementService.getAnnouncementList();
        return CommonResult.success(announcements);
    }
}