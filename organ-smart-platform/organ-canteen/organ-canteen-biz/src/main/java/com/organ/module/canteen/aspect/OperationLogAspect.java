package com.organ.module.canteen.aspect;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.organ.module.canteen.service.operationlog.CanteenOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.time.LocalDateTime;

/**
 * 操作日志切面
 * 自动记录用户的操作行为，包括请求参数、响应数据、IP地址等信息
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {

    @Resource
    private CanteenOperationLogService operationLogService;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 操作日志注解
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface OperationLog {
        /**
         * 操作模块
         */
        String module() default "";

        /**
         * 操作类型
         */
        String type() default "";

        /**
         * 操作内容描述
         */
        String content() default "";

        /**
         * 是否记录请求参数
         */
        boolean recordRequest() default true;

        /**
         * 是否记录响应数据
         */
        boolean recordResponse() default true;

    }

    /**
     * 定义切入点：所有标注了@OperationLog注解的方法
     */
    @Pointcut("@annotation(operationLog)")
    public void operationLogPointcut(OperationLog operationLog) {
    }

    /**
     * 环绕通知：记录操作日志
     */
    @Around("operationLogPointcut(operationLog)")
    public Object around(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Exception exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            // 异步记录操作日志
            try {
                recordOperationLog(joinPoint, operationLog, result, exception, startTime);
            } catch (Exception e) {
                log.error("记录操作日志失败", e);
            }
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(JoinPoint joinPoint, OperationLog operationLog, 
                                   Object result, Exception exception, long startTime) {
        try {
            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }
            
            HttpServletRequest request = attributes.getRequest();
            
            // 获取当前用户信息
            String operator = getCurrentOperator();
            
            // 获取请求参数
            String requestJson = null;
            if (operationLog.recordRequest()) {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    try {
                        requestJson = objectMapper.writeValueAsString(args);
                    } catch (JsonProcessingException e) {
                        requestJson = "序列化失败: " + e.getMessage();
                    }
                }
            }

            // 获取响应数据
            String responseJson = null;
            if (operationLog.recordResponse() && result != null) {
                try {
                    responseJson = objectMapper.writeValueAsString(result);
                } catch (JsonProcessingException e) {
                    responseJson = "序列化失败: " + e.getMessage();
                }
            }

            // 如果发生异常，记录异常信息
            if (exception != null) {
                responseJson = "操作失败: " + exception.getMessage();
            }

            // 获取请求信息用于异步记录
            final String clientIp = getClientIp(request);
            final String userAgent = getUserAgent(request);
            final String finalRequestJson = requestJson;
            final String finalResponseJson = responseJson;
            final String finalOperator = operator;

            // 异步记录日志（避免影响主业务流程）
            new Thread(() -> {
                try {
                    operationLogService.recordLog(
                        operationLog.module(),
                        operationLog.type(),
                        operationLog.content(),
                        clientIp,
                        userAgent,
                        finalRequestJson,
                        finalResponseJson,
                        finalOperator
                    );
                } catch (Exception e) {
                    log.error("异步记录操作日志失败", e);
                }
            }).start();
            
        } catch (Exception e) {
            log.error("记录操作日志异常", e);
        }
    }

    /**
     * 获取当前操作人
     */
    private String getCurrentOperator() {
        // 获取当前HTTP请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            
            // 尝试从请求头获取用户信息
            String userHeader = request.getHeader("X-User-Id");
            if (userHeader != null) {
                return userHeader;
            }
            
            // 尝试从Authorization头解析用户信息
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                // 这里可以根据实际的token解析逻辑来获取用户信息
                return "admin"; // 暂时返回固定值，实际项目中需要解析token
            }
            
            // 尝试从请求参数获取
            String userParam = request.getParameter("userId");
            if (userParam != null) {
                return userParam;
            }
        }
        
        return "system";
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }

    /**
     * 获取用户代理信息
     */
    private String getUserAgent(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "";
    }
}