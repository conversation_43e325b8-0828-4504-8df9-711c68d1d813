package com.organ.module.canteen.service.permission;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.validation.Valid;

import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.mapper.permission.*;
import com.organ.module.canteen.api.permission.dto.*;
import com.organ.module.canteen.convert.permission.*;
import com.hainancrc.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 权限信息 Service 实现类
 */
@Service
public class CanteenPermissionServiceImpl implements CanteenPermissionService {

    @Resource
    private CanteenPermissionMapper permissionMapper;

    /**
     * 创建权限
     * 将创建请求转换为数据库实体并保存
     */
    @Override
    public Long create(CanteenPermissionCreateDTO createDTO) {
        CanteenPermissionDO permission = CanteenPermissionConvert.INSTANCE.convert(createDTO);
        if (permission.getSortOrder() == null) {
            permission.setSortOrder(0);
        }
        if (permission.getVisible() == null) {
            permission.setVisible(1);
        }
        permissionMapper.insert(permission);
        return permission.getId();
    }

    /**
     * 更新权限
     * 根据ID更新权限信息
     */
    @Override
    public void update(CanteenPermissionUpdateDTO updateDTO) {
        CanteenPermissionDO permission = CanteenPermissionConvert.INSTANCE.convert(updateDTO);
        permissionMapper.updateById(permission);
    }

    /**
     * 删除权限
     * 执行逻辑删除操作
     */
    @Override
    public void delete(Long id) {
        permissionMapper.deleteById(id);
    }

    /**
     * 获取所有权限列表
     * 查询所有未删除的权限记录，按排序序号排列
     */
    @Override
    public List<CanteenPermissionDO> getList() {
        return permissionMapper.selectList();
    }

    /**
     * 分页查询权限信息
     * 根据查询条件进行分页查询
     */
    @Override
    public PageResult<CanteenPermissionDO> getPage(CanteenPermissionPageDTO pageDTO) {
        return permissionMapper.selectPage(pageDTO);
    }

    /**
     * 获取权限详情
     * 按ID查询权限完整信息
     */
    @Override
    public CanteenPermissionDO get(Long id) {
        return permissionMapper.selectById(id);
    }

    /**
     * 根据父权限ID获取子权限列表
     * 查询指定父权限下的所有子权限
     */
    @Override
    public List<CanteenPermissionDO> getListByParentId(Long parentId) {
        return permissionMapper.selectListByParentId(parentId);
    }
}