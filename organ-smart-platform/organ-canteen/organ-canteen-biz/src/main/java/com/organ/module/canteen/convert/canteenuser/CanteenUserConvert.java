package com.organ.module.canteen.convert.canteenuser;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.canteenuser.dto.*;
import com.organ.module.canteen.api.canteenuser.vo.*;
import com.organ.module.canteen.entity.*;
import java.util.List;

/**
 * 食堂用户信息对象转换器
 */
@Mapper
public interface CanteenUserConvert {
    CanteenUserConvert INSTANCE = Mappers.getMapper(CanteenUserConvert.class);

    /**
     * 将用户创建请求对象转换为数据库实体对象
     * 
     * @param bean 用户创建请求对象
     * @return 数据库实体对象
     */
    CanteenUserDO convert(CanteenUserCreateDTO bean);

    /**
     * 将用户更新请求对象转换为数据库实体对象
     * 
     * @param bean 用户更新请求对象
     * @return 数据库实体对象
     */
    CanteenUserDO convert(CanteenUserUpdateDTO bean);

    /**
     * 将数据库实体对象转换为响应对象
     * 
     * @param bean 数据库实体对象
     * @return 用户信息响应对象
     */
    CanteenUserRespVO convert(CanteenUserDO bean);

    /**
     * 将数据库实体对象列表转换为响应对象列表
     * 
     * @param list 数据库实体对象列表
     * @return 用户信息响应对象列表
     */
    List<CanteenUserRespVO> convertList(List<CanteenUserDO> list);

    /**
     * 将数据库实体对象分页结果转换为响应对象分页结果
     * 
     * @param page 数据库实体对象分页结果
     * @return 用户信息响应对象分页结果
     */
    PageResult<CanteenUserRespVO> convertPage(PageResult<CanteenUserDO> page);

    /**
     * 将数据库实体对象转换为登录用户信息响应对象
     *
     * @param bean 数据库实体对象
     * @return 登录用户信息响应对象
     */
    LoginUserInfoVO convertToLoginUserInfo(CanteenUserDO bean);
}