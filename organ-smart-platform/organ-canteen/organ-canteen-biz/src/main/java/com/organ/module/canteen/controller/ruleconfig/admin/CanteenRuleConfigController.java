package com.organ.module.canteen.controller.ruleconfig.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigCreateDTO;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigPageDTO;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigUpdateDTO;
import com.organ.module.canteen.api.ruleconfig.vo.CanteenRuleConfigRespVO;
import com.organ.module.canteen.service.ruleconfig.CanteenRuleConfigService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 系统规则配置控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Api(tags = "规则配置管理")
@Slf4j
@RestController
@RequestMapping("/rule-config")
public class CanteenRuleConfigController {

    @Resource
    private CanteenRuleConfigService ruleConfigService;

    @ApiOperation("创建规则配置")
    @PostMapping("/create")
    public CommonResult<Long> createRuleConfig(@Valid @RequestBody CanteenRuleConfigCreateDTO createDTO) {
        Long id = ruleConfigService.createRuleConfig(createDTO);
        return CommonResult.success(id);
    }

    @ApiOperation("更新规则配置")
    @PutMapping("/update")
    public CommonResult<Boolean> updateRuleConfig(@Valid @RequestBody CanteenRuleConfigUpdateDTO updateDTO) {
        ruleConfigService.updateRuleConfig(updateDTO);
        return CommonResult.success(true);
    }

    @ApiOperation("删除规则配置")
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteRuleConfig(@ApiParam("规则配置ID") @RequestParam("id") Long id) {
        ruleConfigService.deleteRuleConfig(id);
        return CommonResult.success(true);
    }

    @ApiOperation("获取规则配置详情")
    @GetMapping("/get")
    public CommonResult<CanteenRuleConfigRespVO> getRuleConfig(@ApiParam("规则配置ID") @RequestParam("id") Long id) {
        CanteenRuleConfigRespVO ruleConfig = ruleConfigService.getRuleConfig(id);
        return CommonResult.success(ruleConfig);
    }

    @ApiOperation("分页查询规则配置")
    @GetMapping("/page")
    public CommonResult<PageResult<CanteenRuleConfigRespVO>> getRuleConfigPage(@Valid CanteenRuleConfigPageDTO pageDTO) {
        PageResult<CanteenRuleConfigRespVO> pageResult = ruleConfigService.getRuleConfigPage(pageDTO);
        return CommonResult.success(pageResult);
    }

    @ApiOperation("启用规则配置")
    @PostMapping("/enable")
    public CommonResult<Boolean> enableRuleConfig(@ApiParam("规则配置ID") @RequestParam("id") Long id) {
        ruleConfigService.enableRuleConfig(id);
        return CommonResult.success(true);
    }

    @ApiOperation("禁用规则配置")
    @PostMapping("/disable")
    public CommonResult<Boolean> disableRuleConfig(@ApiParam("规则配置ID") @RequestParam("id") Long id) {
        ruleConfigService.disableRuleConfig(id);
        return CommonResult.success(true);
    }

    @ApiOperation("获取规则配置列表")
    @GetMapping("/list")
    public CommonResult<List<CanteenRuleConfigRespVO>> getRuleConfigList() {
        List<CanteenRuleConfigRespVO> list = ruleConfigService.getRuleConfigList();
        return CommonResult.success(list);
    }

    @ApiOperation("根据规则编码获取规则配置")
    @GetMapping("/get-by-code")
    public CommonResult<CanteenRuleConfigRespVO> getRuleConfigByCode(@ApiParam("规则编码") @RequestParam("ruleCode") String ruleCode) {
        CanteenRuleConfigRespVO ruleConfig = ruleConfigService.getRuleConfigByCode(ruleCode);
        return CommonResult.success(ruleConfig);
    }

}