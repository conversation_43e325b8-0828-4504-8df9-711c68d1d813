package com.organ.module.canteen.service.rolepermission;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.validation.Valid;

import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.mapper.rolepermission.*;
import com.organ.module.canteen.api.rolepermission.dto.*;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 角色权限关联 Service 实现类
 */
@Service
public class CanteenRolePermissionServiceImpl implements CanteenRolePermissionService {

    @Resource
    private CanteenRolePermissionMapper rolePermissionMapper;

    /**
     * 为角色分配权限
     * 删除角色原有的所有权限，然后分配新的权限集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignRolePermissions(CanteenRolePermissionAssignDTO assignDTO) {
        // 先删除角色原有的所有权限
        rolePermissionMapper.deleteByRoleId(assignDTO.getRoleId());
        
        // 批量插入新的权限关联
        for (Long permissionId : assignDTO.getPermissionIds()) {
            CanteenRolePermissionDO rolePermission = CanteenRolePermissionDO.builder()
                    .roleId(assignDTO.getRoleId())
                    .permissionId(permissionId)
                    .createTime(LocalDateTime.now())
                    .deleted(0)
                    .build();
            rolePermissionMapper.insert(rolePermission);
        }
    }

    /**
     * 根据角色ID获取权限ID列表
     * 查询指定角色拥有的所有权限ID
     */
    @Override
    public List<Long> getPermissionIdsByRoleId(Long roleId) {
        return rolePermissionMapper.selectPermissionIdsByRoleId(roleId);
    }

    /**
     * 根据权限ID获取角色ID列表
     * 查询拥有指定权限的所有角色ID
     */
    @Override
    public List<Long> getRoleIdsByPermissionId(Long permissionId) {
        return rolePermissionMapper.selectRoleIdsByPermissionId(permissionId);
    }

    /**
     * 根据角色ID列表获取所有权限ID
     * 查询多个角色拥有的所有权限ID（去重）
     */
    @Override
    public List<Long> getPermissionIdsByRoleIds(Collection<Long> roleIds) {
        return rolePermissionMapper.selectPermissionIdsByRoleIds(roleIds);
    }

    /**
     * 删除角色的所有权限
     * 当删除角色时，同时删除该角色的所有权限关联
     */
    @Override
    public void deleteByRoleId(Long roleId) {
        rolePermissionMapper.deleteByRoleId(roleId);
    }

    /**
     * 删除权限的所有角色关联
     * 当删除权限时，同时删除该权限的所有角色关联
     */
    @Override
    public void deleteByPermissionId(Long permissionId) {
        rolePermissionMapper.deleteByPermissionId(permissionId);
    }
}