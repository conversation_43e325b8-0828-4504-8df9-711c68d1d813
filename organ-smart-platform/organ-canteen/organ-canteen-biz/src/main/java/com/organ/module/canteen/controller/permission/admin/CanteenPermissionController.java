package com.organ.module.canteen.controller.permission.admin;

import com.hainancrc.framework.common.pojo.*;
import com.organ.module.canteen.api.permission.dto.*;
import com.organ.module.canteen.api.permission.vo.*;
import com.organ.module.canteen.convert.permission.*;
import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.service.permission.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * 权限管理 Controller
 */
@Api(tags = "权限管理")
@RestController
@RequestMapping("/permission")
@Validated
public class CanteenPermissionController {
    
    @Resource
    private CanteenPermissionService permissionService;

    /**
     * 创建权限
     * 
     * @param createDTO 创建权限的请求参数
     * @return 新创建的权限ID
     */
    @PostMapping("/create")
    @ApiOperation("创建权限")
    public CommonResult<Long> create(@Valid @RequestBody CanteenPermissionCreateDTO createDTO) {
        Long id = permissionService.create(createDTO);
        return success(id);
    }

    /**
     * 更新权限
     * 
     * @param updateDTO 更新权限的请求参数
     * @return 操作结果
     */
    @PutMapping("/update")
    @ApiOperation("更新权限")
    public CommonResult<Boolean> update(@Valid @RequestBody CanteenPermissionUpdateDTO updateDTO) {
        permissionService.update(updateDTO);
        return success(true);
    }

    /**
     * 删除权限
     * 
     * @param id 权限ID
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    @ApiOperation("删除权限")
    @ApiImplicitParam(name = "id", value = "权限ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        permissionService.delete(id);
        return success(true);
    }

    /**
     * 获取所有权限列表
     * 
     * @return 权限信息列表
     */
    @GetMapping("/list")
    @ApiOperation("获得权限列表")
    public CommonResult<List<CanteenPermissionRespVO>> getList() {
        List<CanteenPermissionDO> list = permissionService.getList();
        return success(CanteenPermissionConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页查询权限信息
     * 
     * @param pageDTO 分页查询条件，包含权限名称、权限编码、权限类型等筛选条件
     * @return 分页权限信息
     */
    @GetMapping("/page")
    @ApiOperation("获得权限分页")
    public CommonResult<PageResult<CanteenPermissionRespVO>> getPage(@Valid CanteenPermissionPageDTO pageDTO) {
        PageResult<CanteenPermissionDO> pageResult = permissionService.getPage(pageDTO);
        return success(CanteenPermissionConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 根据ID获取权限详情
     * 
     * @param id 权限ID
     * @return 权限详细信息
     */
    @GetMapping("/get")
    @ApiOperation("获得权限详情")
    @ApiImplicitParam(name = "id", value = "权限ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<CanteenPermissionRespVO> get(@RequestParam("id") Long id) {
        CanteenPermissionDO permission = permissionService.get(id);
        return success(CanteenPermissionConvert.INSTANCE.convert(permission));
    }

    /**
     * 根据父权限ID获取子权限列表
     * 
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    @GetMapping("/listByParent")
    @ApiOperation("获得子权限列表")
    @ApiImplicitParam(name = "parentId", value = "父权限ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<List<CanteenPermissionRespVO>> getListByParentId(@RequestParam("parentId") Long parentId) {
        List<CanteenPermissionDO> list = permissionService.getListByParentId(parentId);
        return success(CanteenPermissionConvert.INSTANCE.convertList(list));
    }

}