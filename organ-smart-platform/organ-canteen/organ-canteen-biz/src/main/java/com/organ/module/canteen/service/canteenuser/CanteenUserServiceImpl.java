package com.organ.module.canteen.service.canteenuser;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.validation.Valid;

import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.mapper.canteenuser.*;
import com.organ.module.canteen.api.canteenuser.dto.*;
import com.organ.module.canteen.convert.canteenuser.*;
import com.hainancrc.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 食堂用户信息 Service 实现类
 */
@Service
public class CanteenUserServiceImpl implements CanteenUserService {

    @Resource
    private CanteenUserMapper canteenUserMapper;

    /**
     * 创建新用户
     * 将DTO对象转换为DO对象并保存到数据库
     */
    @Override
    public Long create(CanteenUserCreateDTO createDTO) {
        // 将创建请求对象转换为数据库实体对象
        CanteenUserDO canteenUser = CanteenUserConvert.INSTANCE.convert(createDTO);
        // 插入数据库并获取生成的ID
        canteenUserMapper.insert(canteenUser);
        return canteenUser.getId();
    }

    /**
     * 更新用户信息
     * 将DTO对象转换为DO对象并按ID更新
     */
    @Override
    public void update(CanteenUserUpdateDTO updateDTO) {
        // 将更新请求对象转换为数据库实体对象
        CanteenUserDO updateObj = CanteenUserConvert.INSTANCE.convert(updateDTO);
        // 按ID更新用户信息
        canteenUserMapper.updateById(updateObj);
    }

    /**
     * 删除用户
     * 执行逻辑删除操作
     */
    @Override
    public void delete(Long id) {
        // 执行逻辑删除，将deleted字段设置为1
        canteenUserMapper.deleteById(id);
    }

    /**
     * 获取用户详情
     * 按ID查询用户完整信息
     */
    @Override
    public CanteenUserDO get(Long id) {
        return canteenUserMapper.selectById(id);
    }

    /**
     * 获取所有用户列表
     * 查询所有未删除的用户信息
     */
    @Override
    public List<CanteenUserDO> getList() {
        return canteenUserMapper.selectList();
    }

    /**
     * 分页查询用户信息
     * 根据查询条件进行分页查询
     */
    @Override
    public PageResult<CanteenUserDO> getPage(CanteenUserPageDTO pageDTO) {
        return canteenUserMapper.selectPage(pageDTO);
    }
}