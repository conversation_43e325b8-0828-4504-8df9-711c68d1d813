package com.organ.module.canteen.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.organ.module.enums.CheckInSourceEnum;
import com.organ.module.enums.CheckInTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@TableName("check_in_record")
@EqualsAndHashCode(callSuper = true)
public class CheckInRecordDO extends BaseDO {

    @TableId
    private Long id;

    /**
     * 预约信息id
     */
    private Long bookingId;

    /**
     * 核销方式
     */
    private CheckInTypeEnum checkInType;

    /**
     * 核销时间
     */
    private Date checkInTime;

    /**
     * 核销食堂id
     */
    private Long checkInCanteenId;

    /**
     * 核销食堂code
     */
    private String checkInCanteenCode;

    /**
     * 核销人
     */
    private String checkInPerson;

    /**
     * 核销设备
     */
    private String checkInDevice;

    /**
     * 核销备注
     */
    private String remark;

    /**
     * 核销来源
     */
    private CheckInSourceEnum source;
}
