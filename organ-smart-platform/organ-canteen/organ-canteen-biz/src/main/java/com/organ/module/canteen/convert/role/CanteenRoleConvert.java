package com.organ.module.canteen.convert.role;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.role.dto.*;
import com.organ.module.canteen.api.role.vo.*;
import com.organ.module.canteen.entity.*;
import java.util.List;

/**
 * 角色信息对象转换器
 */
@Mapper
public interface CanteenRoleConvert {
    CanteenRoleConvert INSTANCE = Mappers.getMapper(CanteenRoleConvert.class);

    /**
     * 将创建请求对象转换为数据库实体对象
     * 
     * @param bean 创建请求对象
     * @return 角色数据库实体对象
     */
    CanteenRoleDO convert(CanteenRoleCreateDTO bean);
    
    /**
     * 将更新请求对象转换为数据库实体对象
     * 
     * @param bean 更新请求对象
     * @return 角色数据库实体对象
     */
    CanteenRoleDO convert(CanteenRoleUpdateDTO bean);
    
    /**
     * 将数据库实体对象转换为响应对象
     * 
     * @param bean 数据库实体对象
     * @return 角色信息响应对象
     */
    CanteenRoleRespVO convert(CanteenRoleDO bean);
    
    /**
     * 将数据库实体对象列表转换为响应对象列表
     * 
     * @param list 数据库实体对象列表
     * @return 角色信息响应对象列表
     */
    List<CanteenRoleRespVO> convertList(List<CanteenRoleDO> list);
    
    /**
     * 将数据库实体对象分页结果转换为响应对象分页结果
     * 
     * @param page 数据库实体对象分页结果
     * @return 角色信息响应对象分页结果
     */
    PageResult<CanteenRoleRespVO> convertPage(PageResult<CanteenRoleDO> page);
}