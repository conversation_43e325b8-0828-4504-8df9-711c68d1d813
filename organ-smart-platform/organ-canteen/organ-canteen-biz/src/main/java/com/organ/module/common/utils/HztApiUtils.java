package com.organ.module.common.utils;

import java.util.HashMap;
import java.util.Map;

import com.hainancrc.framework.common.exception.ServiceException;
import com.organ.module.auth.api.dto.HztUserInfoDTO;
import com.organ.module.auth.api.dto.ThirdPartyTokenDTO;
import com.organ.module.auth.config.hztlogin.HztAuthLoginConfig;

import cn.com.digitalhainan.apione.sdk.ContentBody;
import cn.com.digitalhainan.apione.sdk.HttpCaller;
import cn.com.digitalhainan.apione.sdk.HttpParameters;
import cn.com.digitalhainan.apione.sdk.HttpReturn;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 海政通API工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class HztApiUtils {

    /**
     * 通过授权码获取 token
     *
     * @param accessCode 授权码
     * @param clientType 客户端类型
     * @return token
     */
    public static ThirdPartyTokenDTO getTokenByAccessCode(String accessCode, String clientType,
            HztAuthLoginConfig thirdPartyAuthConfig) {
        try {
            log.info("开始获取第三方token，accessCode: {}, clientType: {}", accessCode, clientType);

            // 获取客户端配置
            HztAuthLoginConfig.ClientConfig clientConfig = thirdPartyAuthConfig.getClientConfig(clientType);
            if (clientConfig == null) {
                throw new ServiceException(500, "未找到客户端配置: " + clientType);
            }
            // 构建请求参数JSON
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("appKey", clientConfig.getAppKey());
            requestData.put("appSecret", clientConfig.getAppSecret());
            requestData.put("code", accessCode);
            requestData.put("redirectUri", "");
            String contentJson = JSONUtil.toJsonStr(requestData);
            String apiName = thirdPartyAuthConfig.getTokenApi();

            // 请求ApiOne服务，获取response data 字符串
            JSONObject jsonResponse = callHztApi(apiName, contentJson, thirdPartyAuthConfig, clientType);

            // 解析token信息
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null) {
                throw new ServiceException(500, "第三方API返回数据格式错误");
            }

            ThirdPartyTokenDTO tokenDTO = new ThirdPartyTokenDTO();
            tokenDTO.setAccessToken(data.getStr("accessToken"));
            tokenDTO.setRefreshToken(data.getStr("refreshToken"));
            tokenDTO.setExpiredIn(data.getLong("expiredIn"));

            log.info("成功获取第三方token");
            return tokenDTO;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用第三方token API异常", e);
            throw new ServiceException(500, "获取第三方token失败: " + e.getMessage());
        }
    }

    /**
     * 通过token获取用户信息
     *
     * @param token      token
     * @param clientType 客户端类型
     * @return 用户信息
     */
    public static HztUserInfoDTO getUserInfoByToken(String token, String clientType,
            HztAuthLoginConfig thirdPartyAuthConfig) {
        try {
            log.info("开始获取第三方用户信息，token: {}, clientType: {}", token, clientType);

            // 构建请求参数JSON
            String contentJson = "token";
            String apiName = thirdPartyAuthConfig.getUserInfoApi();

            // 请求ApiOne服务，获取response data 字符串
            JSONObject jsonResponse = callHztApi(apiName, contentJson, thirdPartyAuthConfig, clientType);

            // 解析用户信息
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null) {
                throw new ServiceException(500, "第三方API返回数据格式错误");
            }

            HztUserInfoDTO userInfo = JSONUtil.toBean(data.toString(), HztUserInfoDTO.class);

            log.info("成功获取第三方用户信息");
            return userInfo;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用第三方用户信息 API异常", e);
            throw new ServiceException(500, "获取第三方用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 从HttpReturn对象中提取响应内容
     *
     * @param response HttpReturn响应对象
     * @return 响应内容字符串
     */
    private static String extractResponseBody(HttpReturn response) {
        try {
            // 尝试常见的方法名
            String[] methodNames = { "getBody", "getData", "getContent", "getResult", "toString" };

            for (String methodName : methodNames) {
                try {
                    java.lang.reflect.Method method = response.getClass().getMethod(methodName);
                    Object result = method.invoke(response);
                    if (result != null) {
                        log.debug("使用方法 {} 获取响应内容: {}", methodName, result);
                        return result.toString();
                    }
                } catch (Exception e) {
                    // 忽略方法不存在的异常，继续尝试下一个方法
                    log.debug("方法 {} 不存在或调用失败: {}", methodName, e.getMessage());
                }
            }

            // 如果所有方法都失败，返回toString()结果
            return response.toString();

        } catch (Exception e) {
            log.error("提取响应内容失败", e);
            return response.toString();
        }
    }

    /**
     * 封装第三方API调用
     * 
     * @param apiName              API名称
     * @param contentJson          请求内容JSON字符串
     * @param thirdPartyAuthConfig 第三方认证配置
     * @return 响应内容字符串
     */
    public static JSONObject callHztApi(String apiName, String contentJson,
            HztAuthLoginConfig thirdPartyAuthConfig, String clientType) {
        try {
            log.info("开始调用第三方API，apiName: {}, contentJson: {}", apiName, contentJson);

            // 获取客户端配置
            HztAuthLoginConfig.ClientConfig clientConfig = thirdPartyAuthConfig.getClientConfig(clientType);
            if (clientConfig == null) {
                throw new ServiceException(500, "未找到客户端配置: " + clientType);
            }

            if (!clientConfig.getEnabled()) {
                throw new ServiceException(500, "客户端已禁用: " + clientType);
            }

            String ak = clientConfig.getAppKey();
            String sk = clientConfig.getAppSecret();
            String api = apiName;
            String region = thirdPartyAuthConfig.getRegion();
            String requestUrl = thirdPartyAuthConfig.getRequestUrl();
            ContentBody contentBody = new ContentBody(contentJson);
            Map<String, String> header = new HashMap<>();
            header.put("Uaa-Tenant-Id", thirdPartyAuthConfig.getTenantId());

            // 拼装业务信息
            HttpParameters parameters = HttpParameters.builder()
                    .api(api)
                    .headerParamsMap(header)
                    .region(region)
                    .accessKey(ak)
                    .secretKey(sk)
                    .contentBody(contentBody)
                    .requestUrl(requestUrl)
                    .build();
            // 请求开发服务，获取response
            HttpReturn callResponse = HttpCaller.getInstance().call(parameters);

            log.info("第三方API响应: {}", callResponse.toString());

            // 尝试获取响应内容，使用反射来查找可用的方法
            String responseBody = extractResponseBody(callResponse);
            log.info("第三方API响应DATA: {}", responseBody);

            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);

            // 检查响应状态
            if (!isSuccessResponse(jsonResponse)) {
                String errorMsg = jsonResponse.getStr("message", "获取token失败");
                log.error("第三方API返回错误: {}", errorMsg);
                throw new ServiceException(500, "获取第三方token失败: " + errorMsg);
            }

            return jsonResponse;
        } catch (Exception e) {
            log.error("调用第三方API异常", e);
            throw new ServiceException(500, "调用第三方API失败: " + e.getMessage());
        }
    }

    /**
     * 判断第三方API响应是否成功
     *
     * @param jsonResponse 响应JSON对象
     * @return 是否成功
     */
    private static boolean isSuccessResponse(JSONObject jsonResponse) {
        // 根据第三方API的实际响应格式调整判断逻辑
        Boolean success = jsonResponse.getBool("success");
        return success != null && success;
    }
}
