package com.organ.module.canteen.mapper.operationlog;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.api.operationlog.dto.*;

import org.apache.ibatis.annotations.*;

/**
 * 操作日志审计 Mapper 接口
 */
@Mapper
public interface CanteenOperationLogMapper extends BaseMapperX<CanteenOperationLogDO> {
    
    /**
     * 分页查询操作日志信息
     * 
     * 支持按以下条件进行筛选：
     * - 操作人（精确查询）
     * - 操作类型（精确查询）
     * - 日志模块（精确查询）
     * - 操作内容（模糊查询）
     * - 客户端IP（精确查询）
     * - 操作时间范围（区间查询）
     * 
     * 结果按操作时间倒序排列，便于查看最新的操作记录
     * 
     * @param reqDTO 分页查询请求对象
     * @return 分页查询结果
     */
    default PageResult<CanteenOperationLogDO> selectPage(CanteenOperationLogPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<CanteenOperationLogDO>()
                .eqIfPresent(CanteenOperationLogDO::getOperator, reqDTO.getOperator())
                .eqIfPresent(CanteenOperationLogDO::getOperationType, reqDTO.getOperationType())
                .betweenIfPresent(CanteenOperationLogDO::getOperationTime, reqDTO.getStartTime(), reqDTO.getEndTime())
                .orderByDesc(CanteenOperationLogDO::getOperationTime));
    }
}