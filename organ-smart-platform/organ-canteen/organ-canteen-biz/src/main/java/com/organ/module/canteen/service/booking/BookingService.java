package com.organ.module.canteen.service.booking;

import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.booking.dto.BookingCreateDTO;
import com.organ.module.canteen.api.booking.dto.BookingPageDTO;
import com.organ.module.canteen.api.booking.vo.BookingPageRespVO;
import com.organ.module.canteen.api.booking.vo.BookingRespVO;
import com.organ.module.canteen.api.booking.vo.ReserveRespVO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


public interface BookingService {

    /**
     * 预约信息分页查询
     *
     * @param pageDTO pageDTO
     * @return result
     */
    PageResult<BookingPageRespVO> selectBookingPage(BookingPageDTO pageDTO);

    /**
     * 预约信息详情查询
     *
     * @param id id
     * @return result
     */
    BookingRespVO selectBookingById(Long id);

    /**
     * 预约
     *
     * @param createDTO createDTO
     * @return result
     */
    ReserveRespVO reserve(@Valid BookingCreateDTO createDTO);

    /**
     * 取消预约
     *
     * @param id id
     * @return result
     */
    Boolean cancel(Long id);

    /**
     * 手动核销
     *
     * @param id id
     * @return result
     */
    Boolean manualWriteOff(Long id);

    /**
     * 导出
     *
     * @param response response
     * @param pageDTO pageDTO
     */
    void export(HttpServletResponse response, BookingPageDTO pageDTO);
}
