package com.organ.module.canteen.mapper.rolepermission;

import com.organ.module.canteen.entity.CanteenRolePermissionDO;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色权限关联 Mapper 接口
 */
@Mapper
public interface CanteenRolePermissionMapper extends BaseMapperX<CanteenRolePermissionDO> {

    /**
     * 根据角色ID查询权限ID列表
     * 
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    default List<Long> selectPermissionIdsByRoleId(Long roleId) {
        return selectList(new LambdaQueryWrapperX<CanteenRolePermissionDO>()
                .eq(CanteenRolePermissionDO::getRoleId, roleId))
                .stream()
                .map(CanteenRolePermissionDO::getPermissionId)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据权限ID查询角色ID列表
     * 
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    default List<Long> selectRoleIdsByPermissionId(Long permissionId) {
        return selectList(new LambdaQueryWrapperX<CanteenRolePermissionDO>()
                .eq(CanteenRolePermissionDO::getPermissionId, permissionId))
                .stream()
                .map(CanteenRolePermissionDO::getRoleId)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据角色ID删除所有关联的权限
     * 
     * @param roleId 角色ID
     */
    default void deleteByRoleId(Long roleId) {
        delete(new LambdaQueryWrapperX<CanteenRolePermissionDO>()
                .eq(CanteenRolePermissionDO::getRoleId, roleId));
    }
    
    /**
     * 根据权限ID删除所有关联的角色
     * 
     * @param permissionId 权限ID
     */
    default void deleteByPermissionId(Long permissionId) {
        delete(new LambdaQueryWrapperX<CanteenRolePermissionDO>()
                .eq(CanteenRolePermissionDO::getPermissionId, permissionId));
    }
    
    /**
     * 根据角色ID列表查询所有权限ID
     * 
     * @param roleIds 角色ID列表
     * @return 权限ID列表
     */
    default List<Long> selectPermissionIdsByRoleIds(Collection<Long> roleIds) {
        if (roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<CanteenRolePermissionDO>()
                .in(CanteenRolePermissionDO::getRoleId, roleIds))
                .stream()
                .map(CanteenRolePermissionDO::getPermissionId)
                .distinct()
                .collect(Collectors.toList());
    }
}