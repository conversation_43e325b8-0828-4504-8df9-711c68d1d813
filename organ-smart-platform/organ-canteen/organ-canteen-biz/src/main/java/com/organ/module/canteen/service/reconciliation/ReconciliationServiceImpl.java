package com.organ.module.canteen.service.reconciliation;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.excel.core.utils.ExcelUtils;
import com.organ.module.canteen.api.reconciliation.dto.ReconciliationPageDTO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationExportVO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationPageRespVO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationStatVO;
import com.organ.module.canteen.entity.MealPeriodDO;
import com.organ.module.canteen.mapper.mealperiod.MealPeriodMapper;
import com.organ.module.canteen.mapper.reconciliation.ReconciliationMapper;
import com.organ.module.common.utils.time.DateUtils;
import com.organ.module.common.utils.time.TimeRange;
import com.organ.module.enums.BookingStatusEnum;
import com.organ.module.enums.ReconciliationTypeEnum;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Slf4j
@Service
public class ReconciliationServiceImpl implements ReconciliationService {


    @Resource
    private MealPeriodMapper mealPeriodMapper;

    @Resource
    private ReconciliationMapper reconciliationMapper;

    @Override
    public ReconciliationStatVO getStatisticsInfo(Long canteenId, ReconciliationTypeEnum type) {
        if (canteenId == null || type == null) {
            return new ReconciliationStatVO();
        }

        LocalDateTime currentTime = LocalDateTime.now();
        LocalDate currentDate = currentTime.toLocalDate();
        LocalTime currentLocalTime = currentTime.toLocalTime();

        switch (type) {
            case MEAL:
                MealPeriodDO targetPeriod = getTargetMealPeriod(canteenId, currentLocalTime);
                if (targetPeriod != null) {
                    return reconciliationMapper.getReconciliationStatByMeal(
                            canteenId, targetPeriod.getId(), currentDate.atStartOfDay());
                }
                return new ReconciliationStatVO();
            case DAY:
                return reconciliationMapper.getReconciliationStatByRange(
                        canteenId, TimeRange.ofDay(currentDate));
            case MONTH:
                return reconciliationMapper.getReconciliationStatByRange(
                        canteenId, TimeRange.ofMonth(currentDate));
            default:
                return new ReconciliationStatVO();
        }
    }

    @Override
    public PageResult<ReconciliationPageRespVO> getReconciliationPage(ReconciliationPageDTO pageDTO) {
        if (pageDTO.getType() == null) {
            return PageResult.empty();
        }

        Page<ReconciliationPageRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDate currentDate = currentTime.toLocalDate();
        LocalTime currentLocalTime = currentTime.toLocalTime();

        Page<ReconciliationPageRespVO> pageResult;
        switch (pageDTO.getType()) {
            case MEAL:
                MealPeriodDO targetPeriod = getTargetMealPeriod(pageDTO.getCanteenId(), currentLocalTime);
                if (targetPeriod != null) {
                    pageResult = reconciliationMapper.getReconciliationPageByMeal(
                            page, pageDTO, targetPeriod.getId(), currentDate.atStartOfDay());
                } else {
                    pageResult = new Page<>();
                }
                break;
            case DAY:
                pageResult = reconciliationMapper.getReconciliationPageByRange(
                        page, pageDTO, TimeRange.ofDay(currentDate));
                break;
            case MONTH:
                pageResult = reconciliationMapper.getReconciliationPageByRange(
                        page, pageDTO, TimeRange.ofMonth(currentDate));
                break;
            default:
                pageResult = new Page<>();
                break;
        }

        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public void export(HttpServletResponse response, ReconciliationPageDTO pageDTO) {

        LocalDateTime currentTime = LocalDateTime.now();
        LocalDate currentDate = currentTime.toLocalDate();
        LocalTime currentLocalTime = currentTime.toLocalTime();

        String fileName = currentDate + "-对账信息.xlsx";

        try {
            List<ReconciliationExportVO> exportList = null;
            if (pageDTO.getType() == null) {
                ExcelUtils.write(response, fileName, "sheet 1", ReconciliationExportVO.class, exportList);
                return;
            }


            switch (pageDTO.getType()) {
                case MEAL:
                    MealPeriodDO targetPeriod = getTargetMealPeriod(pageDTO.getCanteenId(), currentLocalTime);
                    if (targetPeriod != null) {
                        exportList = reconciliationMapper.getReconciliationExportListByMeal(
                                pageDTO, targetPeriod.getId(), currentDate.atStartOfDay());
                    }
                    break;
                case DAY:
                    exportList = reconciliationMapper.getReconciliationExportListByRange(
                            pageDTO, TimeRange.ofDay(currentDate));
                    break;
                case MONTH:
                    exportList = reconciliationMapper.getReconciliationExportListByRange(
                            pageDTO, TimeRange.ofMonth(currentDate));
                    break;
                default:
                    break;
            }

            if (CollectionUtils.isNotEmpty(exportList)) {
                exportList.forEach(item -> {
                    // TODO: 开卡食堂、就餐食堂、卡类别、用户类别转码
                    item.setStatus(BookingStatusEnum.getDescriptionByCode(item.getStatus()));
                });
            }

            ExcelUtils.write(response, fileName, "sheet 1", ReconciliationExportVO.class, exportList);

        } catch (Exception e) {
            log.error("导出对账信息失败", e);
        }
    }

    /**
     * 获取目标餐次时间段
     * @param canteenId 餐厅ID
     * @param currentLocalTime 当前时间
     * @return 目标餐次时间段
     */
    private MealPeriodDO getTargetMealPeriod(Long canteenId, LocalTime currentLocalTime) {
        List<MealPeriodDO> periodList = mealPeriodMapper.getMealPeriodByCanteenId(canteenId);
        if (ObjectUtil.isEmpty(periodList)) {
            return null;
        }

        MealPeriodDO currentPeriod = null;
        MealPeriodDO lastPastPeriod = null;

        for (MealPeriodDO period : periodList) {
            if (DateUtils.isIn(currentLocalTime, period.getStartTime(), period.getEndTime())) {
                currentPeriod = period;
            } else if (period.getEndTime().isBefore(currentLocalTime)) {
                if (lastPastPeriod == null || period.getEndTime().isAfter(lastPastPeriod.getEndTime())) {
                    lastPastPeriod = period;
                }
            }
        }

        return currentPeriod != null ? currentPeriod : lastPastPeriod;
    }

}
