package com.organ.module.canteen.controller.department.admin;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.department.dto.CanteenDepartmentCreateDTO;
import com.organ.module.canteen.api.department.dto.CanteenDepartmentPageDTO;
import com.organ.module.canteen.api.department.dto.CanteenDepartmentUpdateDTO;
import com.organ.module.canteen.api.department.vo.CanteenDepartmentRespVO;
import com.organ.module.canteen.service.department.CanteenDepartmentService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 部门信息控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Api(tags = "部门管理")
@Slf4j
@RestController
@RequestMapping("/department")
public class CanteenDepartmentController {

    @Resource
    private CanteenDepartmentService departmentService;

    @ApiOperation("创建部门")
    @PostMapping(value = "/create")
    public CommonResult<Long> createDepartment(@Valid @RequestBody CanteenDepartmentCreateDTO createDTO) {
        Long id = departmentService.create(createDTO);
        return CommonResult.success(id);
    }

    @ApiOperation("更新部门")
    @PutMapping("/update")
    public CommonResult<Boolean> updateDepartment(@Valid @RequestBody CanteenDepartmentUpdateDTO updateDTO) {
        departmentService.update(updateDTO);
        return CommonResult.success(true);
    }

    @ApiOperation("删除部门")
    @PostMapping("/delete/{id}")
    public CommonResult<Boolean> deleteDepartment(@ApiParam("部门ID") @PathVariable("id") Long id) {
        departmentService.delete(id);
        return CommonResult.success(true);
    }

    @ApiOperation("获取部门详情")
    @GetMapping("/get")
    public CommonResult<CanteenDepartmentRespVO> getDepartment(@ApiParam("部门ID") @RequestParam("id") Long id) {
        CanteenDepartmentRespVO department = departmentService.get(id);
        return CommonResult.success(department);
    }

    @ApiOperation("分页查询部门")
    @GetMapping("/page")
    public CommonResult<PageResult<CanteenDepartmentRespVO>> getDepartmentPage(@Valid CanteenDepartmentPageDTO pageDTO) {
        PageResult<CanteenDepartmentRespVO> pageResult = departmentService.getPage(pageDTO);
        return CommonResult.success(pageResult);
    }

    @ApiOperation("获取部门列表")
    @GetMapping("/list")
    public CommonResult<List<CanteenDepartmentRespVO>> getDepartmentList() {
        List<CanteenDepartmentRespVO> departments = departmentService.getList();
        return CommonResult.success(departments);
    }

    @ApiOperation("获取部门树")
    @GetMapping("/tree")
    public CommonResult<List<CanteenDepartmentRespVO>> getDepartmentTree() {
        List<CanteenDepartmentRespVO> departmentTree = departmentService.getDepartmentTree();
        return CommonResult.success(departmentTree);
    }

    @ApiOperation("根据编码查询部门")
    @GetMapping("/getByCode")
    public CommonResult<CanteenDepartmentRespVO> getDepartmentByCode(@ApiParam("部门编码") @RequestParam("departmentCode") String departmentCode) {
        CanteenDepartmentRespVO department = departmentService.getByCode(departmentCode);
        return CommonResult.success(department);
    }
}