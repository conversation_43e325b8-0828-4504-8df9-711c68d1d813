package com.organ.module.canteen.service.permission;

import java.util.*;
import javax.validation.*;

import com.organ.module.canteen.api.permission.dto.*;
import com.organ.module.canteen.api.permission.vo.*;
import com.organ.module.canteen.entity.*;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 权限信息 Service 接口
 */
public interface CanteenPermissionService {
    
    /**
     * 创建权限
     * 
     * 新增一个权限记录，用于系统权限控制
     *
     * @param createDTO 创建权限的请求参数
     * @return 新创建的权限ID
     */
    Long create(@Valid CanteenPermissionCreateDTO createDTO);
    
    /**
     * 更新权限
     * 
     * 根据ID更新权限的基本信息
     *
     * @param updateDTO 更新权限的请求参数，包含ID和待更新的字段
     */
    void update(@Valid CanteenPermissionUpdateDTO updateDTO);
    
    /**
     * 删除权限
     * 
     * 根据ID逻辑删除权限记录
     *
     * @param id 权限ID
     */
    void delete(Long id);
    
    /**
     * 获取所有权限列表
     * 
     * 查询数据库中所有未删除的权限记录，按排序序号排列
     *
     * @return 权限信息列表
     */
    List<CanteenPermissionDO> getList();
    
    /**
     * 分页查询权限信息
     * 
     * 支持按权限名称、权限编码、权限类型、状态等条件进行筛选查询
     *
     * @param pageDTO 分页查询条件，包含页码、页大小及筛选条件
     * @return 分页权限信息结果
     */
    PageResult<CanteenPermissionDO> getPage(CanteenPermissionPageDTO pageDTO);
    
    /**
     * 根据ID获取权限详细信息
     * 
     * 查询指定ID的权限完整信息
     *
     * @param id 权限ID
     * @return 权限详细信息，如果权限不存在则返回null
     */
    CanteenPermissionDO get(Long id);
    
    /**
     * 根据父权限ID获取子权限列表
     * 
     * 查询指定父权限下的所有子权限
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<CanteenPermissionDO> getListByParentId(Long parentId);
    
}