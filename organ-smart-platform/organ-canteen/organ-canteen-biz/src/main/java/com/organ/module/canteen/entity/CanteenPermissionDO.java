package com.organ.module.canteen.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 权限信息 DO
 */
@TableName("canteen_permission")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CanteenPermissionDO extends BaseDO {

    /**
     * 权限ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 权限名称
     */
    private String permissionName;
    
    /**
     * 权限编码
     */
    private String permissionCode;
    
    /**
     * 权限类型
     * MENU-菜单 BUTTON-按钮 API-接口
     */
    private String permissionType;
    
    /**
     * 父权限ID
     */
    private Long parentId;
    
    /**
     * 路由路径
     */
    private String path;
    
    /**
     * 组件路径
     */
    private String component;
    
    /**
     * 图标
     */
    private String icon;
    
    /**
     * 排序序号
     */
    private Integer sortOrder;
    
    /**
     * 是否显示
     * 0-隐藏 1-显示
     */
    private Integer visible;
    
    /**
     * 状态
     * ACTIVE-启用 DISABLED-禁用
     */
    private String status;
    
    /**
     * 权限描述
     */
    private String description;

}