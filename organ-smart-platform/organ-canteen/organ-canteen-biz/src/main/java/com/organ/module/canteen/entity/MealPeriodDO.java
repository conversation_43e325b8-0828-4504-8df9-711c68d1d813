package com.organ.module.canteen.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalTime;

/**
 * 食堂餐次信息
 *
 */
@Data
@TableName("canteen_meal_period")
@EqualsAndHashCode(callSuper = true)
public class MealPeriodDO extends BaseDO {

    @TableId
    private Long id;

    /**
     * 食堂id
     */
    private Long canteenId;

    /**
     * 餐次名称
     */
    private String mealName;

    /**
     * 餐次开始时间
     */
    private LocalTime startTime;

    /**
     * 餐次结束时间
     */
    private LocalTime endTime;

    /**
     * 提前预约开启时间
     */
    private LocalTime advanceBookingOpenTime;

    /**
     * 提前预约截止时间
     */
    private LocalTime advanceBookingCloseTime;

    /**
     * 提前预约取消截止时间
     */
    private LocalTime advanceCancelCloseTime;

    /**
     * 提前预约席位数（-1表示无限制）
     */
    private Integer advanceBookingSeats;

    /**
     * 临时预约开启时间
     */
    private LocalTime tempBookingOpenTime;

    /**
     * 临时预约截止时间
     */
    private LocalTime tempBookingCloseTime;

    /**
     * 临时预约取消截止时间
     */
    private LocalTime tempCancelCloseTime;

    /**
     * 临时预约席位数
     */
    private Integer tempBookingSeats;

    /**
     * 餐次营业状态(AVAILABLE-营业 UNAVAILABLEE-未营业）
     */
    private String status;

    /**
     * 预约可用状态（OPEN-开放预约 CLOSED-关闭预约）
     */
    private String bookingAvailableStatus;

}
