package com.organ.module.canteen.controller.canteenuser.admin;

import com.hainancrc.framework.common.pojo.*;
import com.organ.module.canteen.api.canteenuser.dto.*;
import com.organ.module.canteen.api.canteenuser.vo.*;
import com.organ.module.canteen.convert.canteenuser.*;
import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.service.canteenuser.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.hainancrc.framework.common.pojo.CommonResult.success;


@Api(tags = "用户信息管理")
@RestController
@RequestMapping("/canteenUser")
@Validated
public class CanteenUserController {
    
    @Resource
    private CanteenUserService canteenUserService;

    /**
     * 新增食堂用户
     * 
     * @param createDTO 用户创建请求对象，包含用户基本信息
     * @return 新创建用户的ID
     */
    @PostMapping("/create")
    @ApiOperation("新增用户")
    public CommonResult<Long> create(@Valid @RequestBody CanteenUserCreateDTO createDTO) {
        return success(canteenUserService.create(createDTO));
    }

    /**
     * 更新食堂用户信息
     * 
     * @param updateDTO 用户更新请求对象，包含需要更新的用户信息
     * @return 更新结果
     */
    @PutMapping("/update")
    @ApiOperation("更新用户")
    public CommonResult<Boolean> update(@Valid @RequestBody CanteenUserUpdateDTO updateDTO) {
        canteenUserService.update(updateDTO);
        return success(true);
    }

    /**
     * 获取所有食堂用户列表
     * 
     * @return 用户信息列表
     */
    @GetMapping("/list")
    @ApiOperation("获得用户列表")
    public CommonResult<List<CanteenUserRespVO>> getList() {
        List<CanteenUserDO> list = canteenUserService.getList();
        return success(CanteenUserConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页查询食堂用户信息
     * 
     * @param pageDTO 分页查询条件，包含用户名、手机号、部门等筛选条件
     * @return 分页用户信息
     */
    @GetMapping("/page")
    @ApiOperation("获得用户分页")
    public CommonResult<PageResult<CanteenUserRespVO>> getPage(@Valid CanteenUserPageDTO pageDTO) {
        PageResult<CanteenUserDO> pageResult = canteenUserService.getPage(pageDTO);
        return success(CanteenUserConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 删除食堂用户
     * 
     * @param id 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @ApiOperation("删除用户")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        canteenUserService.delete(id);
        return success(true);
    }

    /**
     * 根据ID获取食堂用户详情
     * 
     * @param id 用户ID
     * @return 用户详细信息
     */
    @GetMapping("/get")
    @ApiOperation("获得用户详情")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<CanteenUserRespVO> get(@RequestParam("id") Long id) {
        CanteenUserDO canteenUser = canteenUserService.get(id);
        return success(CanteenUserConvert.INSTANCE.convert(canteenUser));
    }
}