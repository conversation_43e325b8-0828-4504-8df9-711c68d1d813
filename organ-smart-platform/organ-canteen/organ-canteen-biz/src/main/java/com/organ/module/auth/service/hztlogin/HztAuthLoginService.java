package com.organ.module.auth.service.hztlogin;

import com.organ.module.auth.api.dto.AuthLoginDTO;
import com.organ.module.auth.api.vo.AuthUserInfoVO;
import com.organ.module.canteen.entity.CanteenUserDO;

/**
 * 第三方登录服务接口
 *
 * <AUTHOR>
 */
public interface HztAuthLoginService {

    /**
     * 通过accessToken进行第三方登录
     *
     * @param loginDTO 登录请求参数
     * @return 登录结果
     */
    AuthUserInfoVO loginByAccessToken(AuthLoginDTO loginDTO);

    /**
     * 通过token获取用户的信息
     * 
     * @param token      token
     * @param clientType 客户端类型
     * @param cache      是否缓存
     * @return 用户信息
     * @throws ServiceException 如果用户未登录或token已过期
     */
    CanteenUserDO getUserInfoByToken(String token, String clientType, Boolean cache, Long expiredIn);

}
