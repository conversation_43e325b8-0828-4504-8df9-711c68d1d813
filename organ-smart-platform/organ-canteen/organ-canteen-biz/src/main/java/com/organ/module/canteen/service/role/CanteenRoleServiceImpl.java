package com.organ.module.canteen.service.role;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.validation.Valid;

import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.mapper.role.*;
import com.organ.module.canteen.api.role.dto.*;
import com.organ.module.canteen.convert.role.*;
import com.hainancrc.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 角色信息 Service 实现类
 */
@Service
public class CanteenRoleServiceImpl implements CanteenRoleService {

    @Resource
    private CanteenRoleMapper roleMapper;

    /**
     * 创建角色
     * 将创建请求转换为数据库实体并保存
     */
    @Override
    public Long create(CanteenRoleCreateDTO createDTO) {
        CanteenRoleDO role = CanteenRoleConvert.INSTANCE.convert(createDTO);
        role.setUserCount(0);
        roleMapper.insert(role);
        return role.getId();
    }

    /**
     * 更新角色
     * 根据ID更新角色信息
     */
    @Override
    public void update(CanteenRoleUpdateDTO updateDTO) {
        CanteenRoleDO role = CanteenRoleConvert.INSTANCE.convert(updateDTO);
        roleMapper.updateById(role);
    }

    /**
     * 删除角色
     * 执行逻辑删除操作
     */
    @Override
    public void delete(Long id) {
        roleMapper.deleteById(id);
    }

    /**
     * 获取所有角色列表
     * 查询所有未删除的角色记录
     */
    @Override
    public List<CanteenRoleDO> getList() {
        return roleMapper.selectList();
    }

    /**
     * 分页查询角色信息
     * 根据查询条件进行分页查询
     */
    @Override
    public PageResult<CanteenRoleDO> getPage(CanteenRolePageDTO pageDTO) {
        return roleMapper.selectPage(pageDTO);
    }

    /**
     * 获取角色详情
     * 按ID查询角色完整信息
     */
    @Override
    public CanteenRoleDO get(Long id) {
        return roleMapper.selectById(id);
    }
}