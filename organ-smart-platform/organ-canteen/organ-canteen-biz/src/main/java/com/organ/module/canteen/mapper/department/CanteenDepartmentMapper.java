package com.organ.module.canteen.mapper.department;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.api.department.dto.*;

import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 部门信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper
public interface CanteenDepartmentMapper extends BaseMapperX<CanteenDepartmentDO> {
    
    /**
     * 分页查询部门信息
     * @param reqDTO 分页查询请求对象
     * @return 分页查询结果
     */
    default PageResult<CanteenDepartmentDO> selectPage(CanteenDepartmentPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<CanteenDepartmentDO>()
                .likeIfPresent(CanteenDepartmentDO::getDepartmentName, reqDTO.getDepartmentName())
                .likeIfPresent(CanteenDepartmentDO::getDepartmentCode, reqDTO.getDepartmentCode())
                .eqIfPresent(CanteenDepartmentDO::getParentId, reqDTO.getParentId())
                .orderByDesc(CanteenDepartmentDO::getCreateTime));
    }

    /**
     * 查询所有部门列表（用于树形结构）
     *
     * @return 部门列表
     */
    default List<CanteenDepartmentDO> selectAllList() {
        return selectList(new LambdaQueryWrapperX<CanteenDepartmentDO>()
                .orderByAsc(CanteenDepartmentDO::getDepartmentCode));
    }

    /**
     * 根据部门编码查询
     *
     * @param departmentCode 部门编码
     * @return 部门信息
     */
    default CanteenDepartmentDO selectByCode(String departmentCode) {
        return selectOne(new LambdaQueryWrapperX<CanteenDepartmentDO>()
                .eq(CanteenDepartmentDO::getDepartmentCode, departmentCode));
    }

    /**
     * 根据父部门ID查询子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    default List<CanteenDepartmentDO> selectByParentId(Long parentId) {
        return selectList(new LambdaQueryWrapperX<CanteenDepartmentDO>()
                .eq(CanteenDepartmentDO::getParentId, parentId)
                .orderByAsc(CanteenDepartmentDO::getDepartmentCode));
    }
}