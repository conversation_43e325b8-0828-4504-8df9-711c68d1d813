package com.organ.module.canteen.service.announcement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementCreateDTO;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementPageDTO;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementUpdateDTO;
import com.organ.module.canteen.api.announcement.vo.CanteenAnnouncementRespVO;

import java.util.List;

/**
 * 公告信息服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface CanteenAnnouncementService {

    /**
     * 创建公告
     *
     * @param createDTO 创建参数
     * @return 公告ID
     */
    Long createAnnouncement(CanteenAnnouncementCreateDTO createDTO);

    /**
     * 更新公告
     *
     * @param updateDTO 更新参数
     */
    void updateAnnouncement(CanteenAnnouncementUpdateDTO updateDTO);

    /**
     * 删除公告
     *
     * @param id 公告ID
     */
    void deleteAnnouncement(Long id);

    /**
     * 根据ID获取公告
     *
     * @param id 公告ID
     * @return 公告信息
     */
    CanteenAnnouncementRespVO getAnnouncement(Long id);

    /**
     * 分页查询公告
     *
     * @param pageDTO 查询参数
     * @return 分页结果
     */
    PageResult<CanteenAnnouncementRespVO> getAnnouncementPage(CanteenAnnouncementPageDTO pageDTO);

    /**
     * 发布公告
     *
     * @param id 公告ID
     */
    void publishAnnouncement(Long id);

    /**
     * 下线公告
     *
     * @param id 公告ID
     */
    void offlineAnnouncement(Long id);

    /**
     * 获取所有公告列表
     *
     * @return 公告列表
     */
    List<CanteenAnnouncementRespVO> getAnnouncementList();
}