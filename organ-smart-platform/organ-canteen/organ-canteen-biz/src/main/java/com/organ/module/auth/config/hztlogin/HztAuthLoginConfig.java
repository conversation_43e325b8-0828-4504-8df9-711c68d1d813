package com.organ.module.auth.config.hztlogin;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 第三方认证配置类
 * 用于管理不同客户端的AK、SK配置信息
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "third-party.auth")
@Data
public class HztAuthLoginConfig {

    /**
     * 第三方认证服务请求URL
     */
    private String requestUrl = "";

    /**
     * 获取token的API名称
     */
    private String tokenApi = "";

    /**
     * 获取用户信息的API名称
     */
    private String userInfoApi = "";

    /**
     * 区域标识
     */
    private String region = "INTER";

    /**
     * 租户ID
     */
    private String tenantId = "";

    /**
     * 不同客户端的配置信息
     * key: 客户端类型（user、operation、admin）
     * value: 客户端配置信息
     */
    private Map<String, ClientConfig> clients;

    /**
     * 客户端配置信息
     */
    @Data
    public static class ClientConfig {
        /**
         * 应用标识（AK）
         */
        private String appKey;

        /**
         * 应用密钥（SK）
         */
        private String appSecret;

        /**
         * 客户端名称
         */
        private String clientName;

        /**
         * 是否启用
         */
        private Boolean enabled = true;
    }

    /**
     * 根据客户端类型获取配置信息
     *
     * @param clientType 客户端类型
     * @return 客户端配置信息
     */
    public ClientConfig getClientConfig(String clientType) {
        if (clients == null) {
            return null;
        }
        return clients.get(clientType);
    }

    /**
     * 获取完整的请求URL
     *
     * @return 请求URL
     */
    public String getRequestUrl() {
        return requestUrl;
    }

}
