package com.organ.module.canteen.mapper.mealquota;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.organ.module.canteen.entity.MealQuotaDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

@Mapper
public interface MealQuotaMapper extends BaseMapperX<MealQuotaDO> {

    // TODO: check 额度信息的唯一性
    MealQuotaDO selectMealQuotaForUpdate(@Param("mealPeriodId") Long mealPeriodId,
                                         @Param("reserveDate") LocalDate reserveDate);

    default Integer deductMealQuota(MealQuotaDO mealQuota) {
        return update(null, new LambdaUpdateWrapper<MealQuotaDO>()
                        .setSql("quota_left = quota_left - 1")
                        .eq(MealQuotaDO::getId, mealQuota.getId())
                        .eq(MealQuotaDO::getPublishStatus, "PUBLISHED")
                        .gt(MealQuotaDO::getQuotaLeft, 0)
                );
    }
}
