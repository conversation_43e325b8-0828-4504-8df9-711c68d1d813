package com.organ.module.canteen.service.ruleconfig;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigCreateDTO;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigPageDTO;
import com.organ.module.canteen.api.ruleconfig.dto.CanteenRuleConfigUpdateDTO;
import com.organ.module.canteen.api.ruleconfig.vo.CanteenRuleConfigRespVO;
import com.organ.module.canteen.entity.CanteenRuleConfigDO;
import com.organ.module.canteen.mapper.ruleconfig.CanteenRuleConfigMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统规则配置服务实现
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class CanteenRuleConfigServiceImpl implements CanteenRuleConfigService {

    @Resource
    private CanteenRuleConfigMapper ruleConfigMapper;

    /**
     * 规则配置状态常量
     */
    private static final String STATUS_ACTIVE = "ACTIVE";
    private static final String STATUS_DISABLED = "DISABLED";

    /**
     * 时间单位常量
     */
    private static final String TIME_UNIT_DAY = "DAY";
    private static final String TIME_UNIT_WEEK = "WEEK";
    private static final String TIME_UNIT_MONTH = "MONTH";

    @Override
    public Long createRuleConfig(CanteenRuleConfigCreateDTO createDTO) {
        // 检查规则编码是否已存在
        CanteenRuleConfigDO existing = ruleConfigMapper.selectByRuleCode(createDTO.getRuleCode());
        if (existing != null) {
            throw new RuntimeException("规则编码已存在");
        }

        CanteenRuleConfigDO ruleConfig = new CanteenRuleConfigDO();
        BeanUtils.copyProperties(createDTO, ruleConfig);
        
        // 设置默认状态
        if (!StringUtils.hasText(createDTO.getStatus())) {
            ruleConfig.setStatus(STATUS_ACTIVE);
        }
        
        // 设置创建人（这里简化处理，实际应该从安全上下文获取）
        ruleConfig.setCreator("admin");
        
        ruleConfigMapper.insert(ruleConfig);
        log.info("创建规则配置成功，ID: {}", ruleConfig.getId());
        
        return ruleConfig.getId();
    }

    @Override
    public void updateRuleConfig(CanteenRuleConfigUpdateDTO updateDTO) {
        CanteenRuleConfigDO existingRuleConfig = ruleConfigMapper.selectById(updateDTO.getId());
        if (existingRuleConfig == null) {
            throw new RuntimeException("规则配置不存在");
        }

        // 如果修改了规则编码，需要检查新编码是否已存在
        if (!existingRuleConfig.getRuleCode().equals(updateDTO.getRuleCode())) {
            CanteenRuleConfigDO existing = ruleConfigMapper.selectByRuleCode(updateDTO.getRuleCode());
            if (existing != null && !existing.getId().equals(updateDTO.getId())) {
                throw new RuntimeException("规则编码已存在");
            }
        }

        CanteenRuleConfigDO ruleConfig = new CanteenRuleConfigDO();
        BeanUtils.copyProperties(updateDTO, ruleConfig);
        
        // 设置更新人
        ruleConfig.setUpdater("admin");
        
        ruleConfigMapper.updateById(ruleConfig);
        log.info("更新规则配置成功，ID: {}", updateDTO.getId());
    }

    @Override
    public void deleteRuleConfig(Long id) {
        CanteenRuleConfigDO ruleConfig = ruleConfigMapper.selectById(id);
        if (ruleConfig == null) {
            throw new RuntimeException("规则配置不存在");
        }

        // 使用MyBatis-Plus的逻辑删除
        ruleConfigMapper.deleteById(id);
        log.info("删除规则配置成功，ID: {}", id);
    }

    @Override
    public CanteenRuleConfigRespVO getRuleConfig(Long id) {
        CanteenRuleConfigDO ruleConfig = ruleConfigMapper.selectById(id);
        if (ruleConfig == null) {
            return null;
        }
        return convertToVO(ruleConfig);
    }

    @Override
    public PageResult<CanteenRuleConfigRespVO> getRuleConfigPage(CanteenRuleConfigPageDTO pageDTO) {
        Page<CanteenRuleConfigDO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<CanteenRuleConfigDO> pageResult = ruleConfigMapper.selectPage(page, pageDTO);
        
        List<CanteenRuleConfigRespVO> list = pageResult.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public void enableRuleConfig(Long id) {
        CanteenRuleConfigDO ruleConfig = ruleConfigMapper.selectById(id);
        if (ruleConfig == null) {
            throw new RuntimeException("规则配置不存在");
        }

        ruleConfig.setStatus(STATUS_ACTIVE);
        ruleConfig.setUpdater("admin");
        ruleConfigMapper.updateById(ruleConfig);
        log.info("启用规则配置成功，ID: {}", id);
    }

    @Override
    public void disableRuleConfig(Long id) {
        CanteenRuleConfigDO ruleConfig = ruleConfigMapper.selectById(id);
        if (ruleConfig == null) {
            throw new RuntimeException("规则配置不存在");
        }

        ruleConfig.setStatus(STATUS_DISABLED);
        ruleConfig.setUpdater("admin");
        ruleConfigMapper.updateById(ruleConfig);
        log.info("禁用规则配置成功，ID: {}", id);
    }

    @Override
    public List<CanteenRuleConfigRespVO> getRuleConfigList() {
        List<CanteenRuleConfigDO> list = ruleConfigMapper.selectList(null);
        return list.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public CanteenRuleConfigRespVO getRuleConfigByCode(String ruleCode) {
        CanteenRuleConfigDO ruleConfig = ruleConfigMapper.selectByRuleCode(ruleCode);
        if (ruleConfig == null) {
            return null;
        }
        return convertToVO(ruleConfig);
    }

    /**
     * 将DO转换为VO
     */
    private CanteenRuleConfigRespVO convertToVO(CanteenRuleConfigDO ruleConfigDO) {
        CanteenRuleConfigRespVO vo = new CanteenRuleConfigRespVO();
        BeanUtils.copyProperties(ruleConfigDO, vo);
        
        // 设置状态描述
        vo.setStatusDesc(getStatusDesc(ruleConfigDO.getStatus()));
        
        // 设置时间单位描述
        vo.setTimeUnitDesc(getTimeUnitDesc(ruleConfigDO.getTimeUnit()));
        
        return vo;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(String status) {
        if (STATUS_ACTIVE.equals(status)) {
            return "启用";
        } else if (STATUS_DISABLED.equals(status)) {
            return "禁用";
        }
        return status;
    }

    /**
     * 获取时间单位描述
     */
    private String getTimeUnitDesc(String timeUnit) {
        if (TIME_UNIT_DAY.equals(timeUnit)) {
            return "天";
        } else if (TIME_UNIT_WEEK.equals(timeUnit)) {
            return "周";
        } else if (TIME_UNIT_MONTH.equals(timeUnit)) {
            return "月";
        }
        return timeUnit;
    }
}