package com.organ.module.canteen.convert.department;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.department.dto.*;
import com.organ.module.canteen.api.department.vo.*;
import com.organ.module.canteen.entity.*;
import java.util.List;

/**
 * 部门信息对象转换器
 */
@Mapper
public interface CanteenDepartmentConvert {
    CanteenDepartmentConvert INSTANCE = Mappers.getMapper(CanteenDepartmentConvert.class);

    /**
     * 将部门创建请求对象转换为数据库实体对象
     * 
     * @param bean 部门创建请求对象
     * @return 数据库实体对象
     */
    CanteenDepartmentDO convert(CanteenDepartmentCreateDTO bean);
    
    /**
     * 将部门更新请求对象转换为数据库实体对象
     * 
     * @param bean 部门更新请求对象
     * @return 数据库实体对象
     */
    CanteenDepartmentDO convert(CanteenDepartmentUpdateDTO bean);
    
    /**
     * 将数据库实体对象转换为响应对象
     * 
     * @param bean 数据库实体对象
     * @return 部门信息响应对象
     */
    CanteenDepartmentRespVO convert(CanteenDepartmentDO bean);
    
    /**
     * 将数据库实体对象列表转换为响应对象列表
     * 
     * @param list 数据库实体对象列表
     * @return 部门信息响应对象列表
     */
    List<CanteenDepartmentRespVO> convertList(List<CanteenDepartmentDO> list);
    
    /**
     * 将数据库实体对象分页结果转换为响应对象分页结果
     * 
     * @param page 数据库实体对象分页结果
     * @return 部门信息响应对象分页结果
     */
    PageResult<CanteenDepartmentRespVO> convertPage(PageResult<CanteenDepartmentDO> page);
}