package com.organ.module.canteen.service.booking;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.organ.module.canteen.entity.BookingDO;
import com.organ.module.canteen.mapper.booking.BookingMapper;
import com.organ.module.canteen.entity.MealPeriodDO;
import com.organ.module.canteen.entity.MealQuotaDO;
import com.organ.module.canteen.mapper.mealquota.MealQuotaMapper;
import com.organ.module.common.utils.time.DateUtils;
import com.organ.module.enums.BookingStatusEnum;
import com.organ.module.enums.BookingTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;

@Slf4j
@Service
public class ReserveService {

    private static final String BOOKING_DEFAULT_REMARK = "无特殊要求";

    @Resource
    private BookingMapper bookingMapper;

    @Resource
    private MealQuotaMapper mealQuotaMapper;



    // TODO >> 对接码引擎 预约码待完成
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Boolean doReserve(LocalTime currentTime, LocalDate reserveDate, MealPeriodDO mealPeriod) {
        LocalDate now = LocalDate.now();
        // TODO: 测试数据
        Long userId = 1L;
        Long mealPeriodId = mealPeriod.getId();

        String context = String.format("用户ID=%d, 用餐日期=%s, 餐段ID=%d", userId, reserveDate, mealPeriodId);

        try {
            if (hasActiveBooking(reserveDate, mealPeriodId, userId)) {
                log.info("预约失败 >> 用户已存在有效预约 >> {}", context);
                return false;
            }

            // 提前预约
            if (reserveDate.isAfter(now) || DateUtils.isIn(currentTime, mealPeriod.getAdvanceBookingOpenTime(), mealPeriod.getAdvanceBookingCloseTime())) {
                BookingDO bookingDO = buildBookingDO(mealPeriodId, reserveDate, BookingTypeEnum.ADVANCED, userId);
                String typeContext = context + ", 预约类型=ADVANCED";

                try {
                    bookingMapper.insert(bookingDO);
                    log.info("预约成功 >> 提前预约已创建 >> {}", typeContext);
                    return true;
                } catch (DuplicateKeyException e) {
                    log.warn("预约失败 >> 数据库唯一索引拦截重复插入 >> {}", typeContext, e);
                    return false;
                }
            }

            // 临时预约
            if (DateUtils.isIn(currentTime, mealPeriod.getTempBookingOpenTime(), mealPeriod.getTempBookingCloseTime())) {
                MealQuotaDO quotaDO = mealQuotaMapper.selectMealQuotaForUpdate(mealPeriodId, reserveDate);
                String tempContext = context + ", 预约类型=TEMPORARY";

                if (ObjectUtil.isEmpty(quotaDO)) {
                    log.warn("预约失败 >> 未查询到配额信息 >> {}", tempContext);
                    return false;
                }

                if (quotaDO.getQuotaLeft() <= 0) {
                    log.info("预约失败 >> 无可用配额 >> {} >> 当前剩余={}", tempContext, quotaDO.getQuotaLeft());
                    return false;
                }

                if (quotaDO.getQuotaLeft() > quotaDO.getQuotaCount()) {
                    log.warn("预约失败 >> 配额异常 >> {} >> quotaLeft={} > quotaCount={}", tempContext, quotaDO.getQuotaLeft(), quotaDO.getQuotaCount());
                    return false;
                }

                int deducted = mealQuotaMapper.deductMealQuota(quotaDO);
                if (deducted > 0) {
                    BookingDO bookingDO = buildBookingDO(mealPeriodId, reserveDate, BookingTypeEnum.TEMPORARY, userId);
                    try {
                        bookingMapper.insert(bookingDO);
                        log.info("预约成功 >> 临时预约已创建 >> {}", tempContext);
                        return true;
                    } catch (DuplicateKeyException e) {
                        log.warn("预约失败 >> 数据库唯一索引拦截重复插入 >> {}", tempContext, e);
                        return false;
                    }
                } else {
                    log.warn("预约失败 >> 扣减配额失败（并发竞争）>> {}", tempContext);
                    return false;
                }
            }


            log.info("预约失败 >> 当前时间不在预约开放时段内 >> {} >> currentTime={}", context, currentTime);
            return false;

        } catch (Exception e) {
            log.error("预约失败 >> 系统异常 >> {}", context, e);
            return false;
        }
    }

    private boolean hasActiveBooking(LocalDate reserveDate, Long mealPeriodId, Long userId) {
        // TODO: 当前用户信息
        return bookingMapper.exists(new LambdaQueryWrapper<BookingDO>()
                .eq(BookingDO::getUserId, userId)
                .eq(BookingDO::getMealDate, reserveDate)
                .eq(BookingDO::getMealPeriodId, mealPeriodId)
                .in(BookingDO::getBookingStatus, BookingStatusEnum.BOOKED, BookingStatusEnum.VERIFIED)
        );
    }

    private BookingDO buildBookingDO(Long mealPeriodId, LocalDate mealDate, BookingTypeEnum bookingType, Long userId) {
        BookingDO bookingDO = new BookingDO();
        bookingDO.setMealPeriodId(mealPeriodId);
        // TODO: 获取当前用户信息
        bookingDO.setUserId(userId);
        bookingDO.setBookingType(bookingType);
        bookingDO.setBookingStatus(BookingStatusEnum.BOOKED);
        bookingDO.setMealDate(mealDate);
        // TODO: 获取预约码
        bookingDO.setBookingCode(null);
        bookingDO.setRemark(BOOKING_DEFAULT_REMARK);
        return bookingDO;
    }
}
