package com.organ.module.canteen.service.announcement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementCreateDTO;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementPageDTO;
import com.organ.module.canteen.api.announcement.dto.CanteenAnnouncementUpdateDTO;
import com.organ.module.canteen.api.announcement.vo.CanteenAnnouncementRespVO;
import com.organ.module.canteen.entity.CanteenAnnouncementDO;
import com.organ.module.canteen.mapper.announcement.CanteenAnnouncementMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 公告信息服务实现
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class CanteenAnnouncementServiceImpl implements CanteenAnnouncementService {

    @Resource
    private CanteenAnnouncementMapper announcementMapper;

    /**
     * 公告状态常量
     */
    private static final String STATUS_DRAFT = "DRAFT";
    private static final String STATUS_PUBLISHED = "PUBLISHED";
    private static final String STATUS_OFFLINE = "OFFLINE";

    @Override
    public Long createAnnouncement(CanteenAnnouncementCreateDTO createDTO) {
        CanteenAnnouncementDO announcement = new CanteenAnnouncementDO();
        BeanUtils.copyProperties(createDTO, announcement);
        
        // 设置默认状态
        if (!StringUtils.hasText(createDTO.getStatus())) {
            announcement.setStatus(STATUS_DRAFT);
        }
        
        // 设置创建人（这里简化处理，实际应该从安全上下文获取）
        announcement.setCreator("admin");
        
        announcementMapper.insert(announcement);
        log.info("创建公告成功，ID: {}", announcement.getId());
        
        return announcement.getId();
    }

    @Override
    public void updateAnnouncement(CanteenAnnouncementUpdateDTO updateDTO) {
        CanteenAnnouncementDO existingAnnouncement = announcementMapper.selectById(updateDTO.getId());
        if (existingAnnouncement == null) {
            throw new RuntimeException("公告不存在");
        }

        CanteenAnnouncementDO announcement = new CanteenAnnouncementDO();
        BeanUtils.copyProperties(updateDTO, announcement);
        
        // 设置更新人
        announcement.setUpdater("admin");
        
        announcementMapper.updateById(announcement);
        log.info("更新公告成功，ID: {}", updateDTO.getId());
    }

    @Override
    public void deleteAnnouncement(Long id) {
        CanteenAnnouncementDO announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            throw new RuntimeException("公告不存在");
        }

        // 使用MyBatis-Plus的逻辑删除
        announcementMapper.deleteById(id);
        log.info("删除公告成功，ID: {}", id);
    }

    @Override
    public CanteenAnnouncementRespVO getAnnouncement(Long id) {
        CanteenAnnouncementDO announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            return null;
        }
        return convertToVO(announcement);
    }

    @Override
    public PageResult<CanteenAnnouncementRespVO> getAnnouncementPage(CanteenAnnouncementPageDTO pageDTO) {
        Page<CanteenAnnouncementDO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<CanteenAnnouncementDO> resultPage = announcementMapper.selectPage(page, pageDTO);
        
        List<CanteenAnnouncementRespVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        return new PageResult<>(voList, resultPage.getTotal());
    }

    @Override
    public void publishAnnouncement(Long id) {
        CanteenAnnouncementDO announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            throw new RuntimeException("公告不存在");
        }

        CanteenAnnouncementDO updateAnnouncement = new CanteenAnnouncementDO();
        updateAnnouncement.setId(id);
        updateAnnouncement.setStatus(STATUS_PUBLISHED);
        updateAnnouncement.setPublishTime(LocalDateTime.now());
        updateAnnouncement.setUpdater("admin");
        
        announcementMapper.updateById(updateAnnouncement);
        log.info("发布公告成功，ID: {}", id);
    }

    @Override
    public void offlineAnnouncement(Long id) {
        CanteenAnnouncementDO announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            throw new RuntimeException("公告不存在");
        }

        CanteenAnnouncementDO updateAnnouncement = new CanteenAnnouncementDO();
        updateAnnouncement.setId(id);
        updateAnnouncement.setStatus(STATUS_OFFLINE);
        updateAnnouncement.setUpdater("admin");
        
        announcementMapper.updateById(updateAnnouncement);
        log.info("下线公告成功，ID: {}", id);
    }

    @Override
    public List<CanteenAnnouncementRespVO> getAnnouncementList() {
        List<CanteenAnnouncementDO> announcements = announcementMapper.selectList(null);
        return announcements.stream()
                .filter(Objects::nonNull)
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 转换为VO对象
     */
    private CanteenAnnouncementRespVO convertToVO(CanteenAnnouncementDO announcement) {
        CanteenAnnouncementRespVO vo = new CanteenAnnouncementRespVO();
        BeanUtils.copyProperties(announcement, vo);
        
        // 设置状态描述
        vo.setStatusDesc(getStatusDesc(announcement.getStatus()));
        
        return vo;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(String status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case STATUS_DRAFT:
                return "草稿";
            case STATUS_PUBLISHED:
                return "已发布";
            case STATUS_OFFLINE:
                return "已下线";
            default:
                return status;
        }
    }
}