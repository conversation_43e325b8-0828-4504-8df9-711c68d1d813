package com.organ.module.canteen.service.department;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import javax.annotation.Resource;
import javax.validation.Valid;

import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.mapper.department.*;
import com.organ.module.canteen.api.department.dto.*;
import com.organ.module.canteen.api.department.vo.*;
import com.organ.module.canteen.convert.department.*;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.common.exception.util.ServiceExceptionUtil;

import java.util.*;
import java.util.stream.Collectors;

import static com.hainancrc.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;

/**
 * 部门信息 Service 实现类
 */
@Service
public class CanteenDepartmentServiceImpl implements CanteenDepartmentService {

    @Resource
    private CanteenDepartmentMapper canteenDepartmentMapper;

    /**
     * 创建新部门
     * 将DTO对象转换为DO对象并保存到数据库
     */
    @Override
    public Long create(@Valid CanteenDepartmentCreateDTO createDTO) {
        // 检查部门编码是否已存在
        CanteenDepartmentDO existingDept = canteenDepartmentMapper.selectByCode(createDTO.getDepartmentCode());
        if (existingDept != null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "部门编码已存在");
        }
        
        // 将创建请求对象转换为数据库实体对象
        CanteenDepartmentDO canteenDepartment = CanteenDepartmentConvert.INSTANCE.convert(createDTO);
        // 插入数据库并获取生成的ID
        canteenDepartmentMapper.insert(canteenDepartment);
        return canteenDepartment.getId();
    }

    /**
     * 更新部门信息
     * 将DTO对象转换为DO对象并按ID更新
     */
    @Override
    public void update(@Valid CanteenDepartmentUpdateDTO updateDTO) {
        // 检查部门是否存在
        CanteenDepartmentDO existingDept = canteenDepartmentMapper.selectById(updateDTO.getId());
        if (existingDept == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "部门不存在");
        }
        
        // 检查部门编码是否重复（排除当前部门）
        CanteenDepartmentDO deptWithSameCode = canteenDepartmentMapper.selectByCode(updateDTO.getDepartmentCode());
        if (deptWithSameCode != null && !deptWithSameCode.getId().equals(updateDTO.getId())) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "部门编码已存在");
        }
        
        // 检查父部门循环引用
        if (updateDTO.getParentId() != null && updateDTO.getParentId().equals(updateDTO.getId())) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "不能将自己设置为父部门");
        }
        
        // 将更新请求对象转换为数据库实体对象
        CanteenDepartmentDO updateObj = CanteenDepartmentConvert.INSTANCE.convert(updateDTO);
        // 按ID更新部门信息
        canteenDepartmentMapper.updateById(updateObj);
    }

    /**
     * 删除部门
     * 执行逻辑删除操作，删除前检查子部门
     */
    @Override
    public void delete(Long id) {
        // 检查部门是否存在
        CanteenDepartmentDO existingDept = canteenDepartmentMapper.selectById(id);
        if (existingDept == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "部门不存在");
        }
        
        // 检查是否有子部门
        List<CanteenDepartmentDO> children = canteenDepartmentMapper.selectByParentId(id);
        if (!children.isEmpty()) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "存在子部门，不能删除");
        }
        
        // 执行逻辑删除，将deleted字段设置为1
        canteenDepartmentMapper.deleteById(id);
    }

    /**
     * 获取部门详情
     * 按ID查询部门完整信息
     */
    @Override
    public CanteenDepartmentRespVO get(Long id) {
        CanteenDepartmentDO department = canteenDepartmentMapper.selectById(id);
        if (department == null) {
            return null;
        }
        
        CanteenDepartmentRespVO respVO = CanteenDepartmentConvert.INSTANCE.convert(department);
        
        // 设置父部门名称
        if (department.getParentId() != null) {
            CanteenDepartmentDO parentDept = canteenDepartmentMapper.selectById(department.getParentId());
            if (parentDept != null) {
                respVO.setParentDepartmentName(parentDept.getDepartmentName());
            }
        }
        
        return respVO;
    }

    /**
     * 根据部门编码查询部门信息
     */
    @Override
    public CanteenDepartmentRespVO getByCode(String departmentCode) {
        CanteenDepartmentDO department = canteenDepartmentMapper.selectByCode(departmentCode);
        return department != null ? CanteenDepartmentConvert.INSTANCE.convert(department) : null;
    }

    /**
     * 获取所有部门列表
     * 查询所有未删除的部门信息
     */
    @Override
    public List<CanteenDepartmentRespVO> getList() {
        List<CanteenDepartmentDO> departments = canteenDepartmentMapper.selectAllList();
        List<CanteenDepartmentRespVO> respVOList = CanteenDepartmentConvert.INSTANCE.convertList(departments);
        
        // 设置父部门名称
        Map<Long, String> parentNameMap = departments.stream()
                .collect(Collectors.toMap(CanteenDepartmentDO::getId, CanteenDepartmentDO::getDepartmentName));
        
        respVOList.forEach(vo -> {
            if (vo.getParentId() != null && parentNameMap.containsKey(vo.getParentId())) {
                vo.setParentDepartmentName(parentNameMap.get(vo.getParentId()));
            }
        });
        
        return respVOList;
    }

    /**
     * 获取部门树形结构
     */
    @Override
    public List<CanteenDepartmentRespVO> getDepartmentTree() {
        List<CanteenDepartmentDO> departments = canteenDepartmentMapper.selectAllList();
        List<CanteenDepartmentRespVO> respVOList = CanteenDepartmentConvert.INSTANCE.convertList(departments);
        
        // 构建树形结构
        return buildDepartmentTree(respVOList, null);
    }

    /**
     * 构建部门树形结构的递归方法
     */
    private List<CanteenDepartmentRespVO> buildDepartmentTree(List<CanteenDepartmentRespVO> allDepartments, Long parentId) {
        return allDepartments.stream()
                .filter(dept -> Objects.equals(dept.getParentId(), parentId))
                .peek(dept -> {
                    List<CanteenDepartmentRespVO> children = buildDepartmentTree(allDepartments, dept.getId());
                    dept.setChildren(children.isEmpty() ? null : children);
                })
                .collect(Collectors.toList());
    }

    /**
     * 分页查询部门信息
     * 根据查询条件进行分页查询
     */
    @Override
    public PageResult<CanteenDepartmentRespVO> getPage(CanteenDepartmentPageDTO pageDTO) {
        // 执行分页查询
        PageResult<CanteenDepartmentDO> pageResult = canteenDepartmentMapper.selectPage(pageDTO);
        
        // 转换为响应VO
        PageResult<CanteenDepartmentRespVO> result = CanteenDepartmentConvert.INSTANCE.convertPage(pageResult);
        
        // 设置父部门名称
        if (result.getList() != null && !result.getList().isEmpty()) {
            Set<Long> parentIds = result.getList().stream()
                    .map(CanteenDepartmentRespVO::getParentId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            if (!parentIds.isEmpty()) {
                List<CanteenDepartmentDO> parentDepts = canteenDepartmentMapper.selectBatchIds(parentIds);
                Map<Long, String> parentNameMap = parentDepts.stream()
                        .collect(Collectors.toMap(CanteenDepartmentDO::getId, CanteenDepartmentDO::getDepartmentName));
                
                result.getList().forEach(vo -> {
                    if (vo.getParentId() != null && parentNameMap.containsKey(vo.getParentId())) {
                        vo.setParentDepartmentName(parentNameMap.get(vo.getParentId()));
                    }
                });
            }
        }
        
        return result;
    }
}