package com.organ.module.canteen.service.operationlog;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.validation.Valid;

import com.organ.module.canteen.entity.*;
import com.organ.module.canteen.mapper.operationlog.*;
import com.organ.module.canteen.api.operationlog.dto.*;
import com.organ.module.canteen.convert.operationlog.*;
import com.hainancrc.framework.common.pojo.PageResult;

import java.util.List;
import java.time.LocalDateTime;

/**
 * 操作日志审计 Service 实现类
 */
@Service
public class CanteenOperationLogServiceImpl implements CanteenOperationLogService {

    @Resource
    private CanteenOperationLogMapper operationLogMapper;

    /**
     * 记录操作日志
     * 异步记录用户操作行为，避免影响主业务流程
     */
    @Override
    public void recordLog(String logModule, String operationType, String operationContent, 
                         String clientIp, String userAgent, String requestJson, String responseJson,
                         String operator) {
        CanteenOperationLogDO logDO = CanteenOperationLogDO.builder()
                .logModule(logModule)
                .operationType(operationType)
                .operationContent(operationContent)
                .clientIp(clientIp)
                .userAgent(userAgent)
                .requestJson(requestJson)
                .responseJson(responseJson)
                .operator(operator)
                .operationTime(LocalDateTime.now())
                .build();
        operationLogMapper.insert(logDO);
    }

    /**
     * 获取所有操作日志列表
     * 查询所有未删除的操作日志记录
     */
    @Override
    public List<CanteenOperationLogDO> getList() {
        return operationLogMapper.selectList();
    }

    /**
     * 分页查询操作日志信息
     * 根据查询条件进行分页查询
     */
    @Override
    public PageResult<CanteenOperationLogDO> getPage(CanteenOperationLogPageDTO pageDTO) {
        return operationLogMapper.selectPage(pageDTO);
    }

    /**
     * 获取操作日志详情
     * 按ID查询操作日志完整信息
     */
    @Override
    public CanteenOperationLogDO get(Long id) {
        return operationLogMapper.selectById(id);
    }

    /**
     * 导出操作日志
     * 根据条件筛选并导出操作日志数据
     */
    @Override
    public String exportLogs(CanteenOperationLogPageDTO pageDTO) {
        // TODO: 实现操作日志导出逻辑
        // 1. 根据条件查询数据
        // 2. 生成Excel文件
        // 3. 返回文件下载地址
        return "操作日志导出功能待实现";
    }
}