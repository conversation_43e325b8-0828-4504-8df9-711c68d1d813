package com.organ.module.canteen.convert.ruleconfig;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.ruleconfig.dto.*;
import com.organ.module.canteen.api.ruleconfig.vo.*;
import com.organ.module.canteen.entity.*;
import java.util.List;

/**
 * 系统规则配置对象转换器
 */
@Mapper
public interface CanteenRuleConfigConvert {
    CanteenRuleConfigConvert INSTANCE = Mappers.getMapper(CanteenRuleConfigConvert.class);

    /**
     * 将创建请求对象转换为数据库实体对象
     * 
     * @param bean 创建请求对象
     * @return 规则配置数据库实体对象
     */
    CanteenRuleConfigDO convert(CanteenRuleConfigCreateDTO bean);
    
    /**
     * 将更新请求对象转换为数据库实体对象
     * 
     * @param bean 更新请求对象
     * @return 规则配置数据库实体对象
     */
    CanteenRuleConfigDO convert(CanteenRuleConfigUpdateDTO bean);
    
    /**
     * 将数据库实体对象转换为响应对象
     * 
     * @param bean 数据库实体对象
     * @return 规则配置信息响应对象
     */
    CanteenRuleConfigRespVO convert(CanteenRuleConfigDO bean);
    
    /**
     * 将数据库实体对象列表转换为响应对象列表
     * 
     * @param list 数据库实体对象列表
     * @return 规则配置信息响应对象列表
     */
    List<CanteenRuleConfigRespVO> convertList(List<CanteenRuleConfigDO> list);
    
    /**
     * 将数据库实体对象分页结果转换为响应对象分页结果
     * 
     * @param page 数据库实体对象分页结果
     * @return 规则配置信息响应对象分页结果
     */
    PageResult<CanteenRuleConfigRespVO> convertPage(PageResult<CanteenRuleConfigDO> page);
}