package com.organ.module.canteen.service.reconciliation;

import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.reconciliation.dto.ReconciliationPageDTO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationPageRespVO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationStatVO;
import com.organ.module.enums.ReconciliationTypeEnum;

import javax.servlet.http.HttpServletResponse;

public interface ReconciliationService {

    /**
     * 获取统计信息
     *
     * @param canteenId 食堂id
     * @param type      类型
     * @return 统计信息
     */
    ReconciliationStatVO getStatisticsInfo(Long canteenId, ReconciliationTypeEnum type);

    /**
     * 获取对账分页
     *
     * @param pageDTO 分页参数
     * @return 对账列表
     */
    PageResult<ReconciliationPageRespVO> getReconciliationPage(ReconciliationPageDTO pageDTO);

    /**
     * 导出
     *
     * @param response response
     * @param pageDTO  pageDTO
     */
    void export(HttpServletResponse response, ReconciliationPageDTO pageDTO);
}
