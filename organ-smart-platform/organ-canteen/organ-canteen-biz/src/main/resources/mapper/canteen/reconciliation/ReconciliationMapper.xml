<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.organ.module.canteen.mapper.reconciliation.ReconciliationMapper">

    <select id="getReconciliationStatByMeal"
            resultType="com.organ.module.canteen.api.reconciliation.vo.ReconciliationStatVO">
        SELECT
            COUNT(1) AS totalReservationCount,
            SUM(CASE WHEN cb.booking_status = 'VERIFIED' THEN 1 ELSE 0 END) AS usedCount,
            SUM(CASE WHEN cb.booking_status = 'BOOKED' THEN 1 ELSE 0 END) AS pendingCount,
            SUM(CASE WHEN cb.booking_status = 'EXPIRED' THEN 1 ELSE 0 END) AS unconsumedCount
        FROM canteen_booking cb
        JOIN canteen_user cu ON cb.user_id = cu.id
        JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
        WHERE cb.deleted = 0
            AND (cu.canteen_id = #{canteenId} OR cm.canteen_id = #{canteenId})
            AND cb.meal_period_id = #{mealPeriodId}
            AND cb.meal_date = #{mealDate}
    </select>

    <select id="getReconciliationStatByRange"
            resultType="com.organ.module.canteen.api.reconciliation.vo.ReconciliationStatVO">
        SELECT
            COUNT(1) AS totalReservationCount,
            SUM(CASE WHEN cb.booking_status = 'VERIFIED' THEN 1 ELSE 0 END) AS usedCount,
            SUM(CASE WHEN cb.booking_status = 'BOOKED' THEN 1 ELSE 0 END) AS pendingCount,
            SUM(CASE WHEN cb.booking_status = 'EXPIRED' THEN 1 ELSE 0 END) AS unconsumedCount
        FROM canteen_booking cb
        JOIN canteen_user cu ON cb.user_id = cu.id
        JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
        WHERE cb.deleted = 0
            AND (cu.canteen_id = #{canteenId} OR cm.canteen_id = #{canteenId})
            AND cb.meal_date BETWEEN #{timeRange.start} AND #{timeRange.end}
    </select>

    <select id="getReconciliationPageByMeal"
            resultType="com.organ.module.canteen.api.reconciliation.vo.ReconciliationPageRespVO">
        SELECT
            cb.id AS id,
            cu.username AS dinerName,
            cu.canteen_id AS cardedCanteen,
            cu.card_type AS cardType,
            cm.canteen_id AS mealCanteen,
            cir.check_in_time AS mealTime,
            cm.meal_name AS mealPeriod,
            cu.user_type AS userType,
            cir.check_in_person AS checkInPerson,
            cb.booking_status AS status
        FROM canteen_booking cb
            JOIN canteen_user cu ON cb.user_id = cu.id
            JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
            LEFT JOIN check_in_record cir ON cb.id = cir.booking_id AND cir.deleted = 0
        WHERE cb.deleted = 0
        <if test="pageDTO.openingCanteenId != null and pageDTO.openingCanteenId != ''">
            AND cu.canteen_id = #{pageDTO.openingCanteenId}
        </if>
        <if test="pageDTO.cardType != null and pageDTO.cardType != ''">
            AND cu.card_type = #{pageDTO.cardType}
        </if>
        <if test="pageDTO.diningCanteenId != null and pageDTO.diningCanteenId != ''">
            AND cm.canteen_id = #{pageDTO.diningCanteenId}
        </if>
        <if test="pageDTO.mealPeriodId != null and pageDTO.mealPeriodId != ''">
            AND cb.meal_period_id = #{pageDTO.mealPeriodId}
            AND cb.meal_date = #{mealDate}
        </if>
    </select>

    <select id="getReconciliationPageByRange"
            resultType="com.organ.module.canteen.api.reconciliation.vo.ReconciliationPageRespVO">
        SELECT
            cb.id AS id,
            cu.username AS dinerName,
            cu.canteen_id AS cardedCanteen,
            cu.card_type AS cardType,
            cm.canteen_id AS mealCanteen,
            cir.check_in_time AS mealTime,
            cm.meal_name AS mealPeriod,
            cu.user_type AS userType,
            cir.check_in_person AS checkInPerson,
            cb.booking_status AS status
        FROM canteen_booking cb
            JOIN canteen_user cu ON cb.user_id = cu.id
            JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
            LEFT JOIN check_in_record cir ON cb.id = cir.booking_id AND cir.deleted = 0
        WHERE cb.deleted = 0
        <if test="pageDTO.openingCanteenId != null and pageDTO.openingCanteenId != ''">
            AND cu.canteen_id = #{pageDTO.openingCanteenId}
        </if>
        <if test="pageDTO.cardType != null and pageDTO.cardType != ''">
            AND cu.card_type = #{pageDTO.cardType}
        </if>
        <if test="pageDTO.diningCanteenId != null and pageDTO.diningCanteenId != ''">
            AND cm.canteen_id = #{pageDTO.diningCanteenId}
        </if>
        <if test="timeRange.start != null">
            AND cb.meal_date >= #{timeRange.start}
        </if>
        <if test="timeRange.end != null">
            AND cb.meal_date &lt;= #{timeRange.end}
        </if>
    </select>

    <select id="getReconciliationExportListByMeal"
            resultType="com.organ.module.canteen.api.reconciliation.vo.ReconciliationExportVO">
        SELECT
            cu.username AS dinerName,
            cu.canteen_id AS cardedCanteen,
            cu.card_type AS cardType,
            cm.canteen_id AS mealCanteen,
            cir.check_in_time AS mealTime,
            cm.meal_name AS mealPeriod,
            cu.user_type AS userType,
            cir.check_in_person AS checkInPerson,
            cb.booking_status AS status
        FROM canteen_booking cb
            JOIN canteen_user cu ON cb.user_id = cu.id
            JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
            LEFT JOIN check_in_record cir ON cb.id = cir.booking_id AND cir.deleted = 0
        WHERE cb.deleted = 0
        <if test="pageDTO.openingCanteenId != null and pageDTO.openingCanteenId != ''">
            AND cu.canteen_id = #{pageDTO.openingCanteenId}
        </if>
        <if test="pageDTO.cardType != null and pageDTO.cardType != ''">
            AND cu.card_type = #{pageDTO.cardType}
        </if>
        <if test="pageDTO.diningCanteenId != null and pageDTO.diningCanteenId != ''">
            AND cm.canteen_id = #{pageDTO.diningCanteenId}
        </if>
        <if test="pageDTO.mealPeriodId != null and pageDTO.mealPeriodId != ''">
            AND cb.meal_period_id = #{pageDTO.mealPeriodId}
            AND cb.meal_date = #{mealDate}
        </if>
    </select>

    <select id="getReconciliationExportListByRange"
            resultType="com.organ.module.canteen.api.reconciliation.vo.ReconciliationExportVO">
        SELECT
            cu.username AS dinerName,
            cu.canteen_id AS cardedCanteen,
            cu.card_type AS cardType,
            cm.canteen_id AS mealCanteen,
            cir.check_in_time AS mealTime,
            cm.meal_name AS mealPeriod,
            cu.user_type AS userType,
            cir.check_in_person AS checkInPerson,
            cb.booking_status AS status
        FROM canteen_booking cb
            JOIN canteen_user cu ON cb.user_id = cu.id
            JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
            LEFT JOIN check_in_record cir ON cb.id = cir.booking_id AND cir.deleted = 0
        WHERE cb.deleted = 0
        <if test="pageDTO.openingCanteenId != null and pageDTO.openingCanteenId != ''">
            AND cu.canteen_id = #{pageDTO.openingCanteenId}
        </if>
        <if test="pageDTO.cardType != null and pageDTO.cardType != ''">
            AND cu.card_type = #{pageDTO.cardType}
        </if>
        <if test="pageDTO.diningCanteenId != null and pageDTO.diningCanteenId != ''">
            AND cm.canteen_id = #{pageDTO.mealCanteenId}
        </if>
        <if test="timeRange.start != null">
            AND cb.meal_date >= #{timeRange.start}
        </if>
        <if test="timeRange.end != null">
            AND cb.meal_date &lt;= #{timeRange.end}
        </if>
    </select>


</mapper>