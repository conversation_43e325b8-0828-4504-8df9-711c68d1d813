<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.organ.module.canteen.mapper.mealquota.MealQuotaMapper">

    <select id="selectMealQuotaForUpdate" resultType="com.organ.module.canteen.entity.MealQuotaDO">
        SELECT *
        FROM canteen_meal_quota
        WHERE meal_period_id = #{mealPeriodId} AND quota_date = #{reserveDate} AND publish_status = 'PUBLISHED' AND deleted = 0
        FOR UPDATE
    </select>
</mapper>