<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.organ.module.canteen.mapper.booking.BookingMapper">


    <select id="selectBookingPage" resultType="com.organ.module.canteen.api.booking.vo.BookingPageRespVO">
        SELECT
            cb.id,
            cu.username,
            cu.dept AS belong_unit,
            ci.canteen_name,
            cm.meal_name AS meal,
            cb.create_time AS booking_date,
            cir.check_in_time AS usage_date,
            cb.booking_status AS status
        FROM canteen_booking cb
            LEFT JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
            LEFT JOIN canteen_info ci ON cm.canteen_id = ci.id AND ci.deleted = 0
            LEFT JOIN canteen_user cu ON cb.user_id = cu.id AND cu.deleted = 0
            LEFT JOIN check_in_record cir ON cb.id = cir.booking_id AND cir.deleted = 0
        WHERE cb.deleted = 0
        <if test="dto.username != null and dto.username != ''">
            AND cu.username LIKE CONCAT('%', #{dto.username}, '%')
        </if>
        <if test="dto.belongUnit != null and dto.belongUnit != ''">
            AND cu.dept LIKE CONCAT('%', #{dto.belongUnit}, '%')
        </if>
        <if test="dto.requestCanteen != null and dto.requestCanteen != ''">
            AND ci.canteen_name LIKE CONCAT('%', #{dto.requestCanteen}, '%')
        </if>
        <if test="dto.meal != null and dto.meal != ''">
            AND cm.meal_name LIKE CONCAT('%', #{dto.meal}, '%')
        </if>
        <if test="dto.startTime != null">
            AND cir.check_in_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            AND cir.check_in_time &lt;= #{dto.endTime}
        </if>
    </select>

    <select id="selectBookingById" resultType="com.organ.module.canteen.api.booking.vo.BookingRespVO">
        SELECT
            cb.id,
            cu.username,
            cu.mobile,
            cu.dept AS belong_unit,
            ci.canteen_name,
            cm.meal_name AS meal,
            cb.create_time AS booking_time,
            cir.check_in_time AS usage_time,
            cb.booking_status AS status,
            cb.remark,
            cir.check_in_person,
            cb.create_time,
            cb.update_time
        FROM canteen_booking cb
            LEFT JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
            LEFT JOIN canteen_info ci ON cm.canteen_id = ci.id AND ci.deleted = 0
            LEFT JOIN canteen_user cu ON cb.user_id = cu.id AND cu.deleted = 0
            LEFT JOIN check_in_record cir ON cb.id = cir.booking_id AND cir.deleted = 0
        WHERE cb.id = #{bookingId} AND cb.deleted = 0
    </select>

    <select id="selectBookingExcelList" resultType="com.organ.module.canteen.api.booking.vo.BookingExportVO">
        SELECT
            cb.id,
            cu.username,
            cu.dept AS belong_unit,
            ci.canteen_name,
            cm.meal_name AS meal,
            cb.create_time AS booking_date,
            cir.check_in_time AS usage_date,
            cb.booking_status AS status
        FROM canteen_booking cb
            LEFT JOIN canteen_meal_period cm ON cb.meal_period_id = cm.id AND cm.deleted = 0
            LEFT JOIN canteen_info ci ON cm.canteen_id = ci.id AND ci.deleted = 0
            LEFT JOIN canteen_user cu ON cb.user_id = cu.id AND cu.deleted = 0
            LEFT JOIN check_in_record cir ON cb.id = cir.booking_id AND cir.deleted = 0
        WHERE cb.deleted = 0
        <if test="dto.username != null and dto.username != ''">
            AND cu.username LIKE CONCAT('%', #{dto.username}, '%')
        </if>
        <if test="dto.belongUnit != null and dto.belongUnit != ''">
            AND cu.dept LIKE CONCAT('%', #{dto.belongUnit}, '%')
        </if>
        <if test="dto.requestCanteen != null and dto.requestCanteen != ''">
            AND ci.canteen_name LIKE CONCAT('%', #{dto.requestCanteen}, '%')
        </if>
        <if test="dto.meal != null and dto.meal != ''">
            AND cm.meal_name LIKE CONCAT('%', #{dto.meal}, '%')
        </if>
        <if test="dto.startTime != null">
            AND cir.check_in_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            AND cir.check_in_time &lt;= #{dto.endTime}
        </if>
    </select>

</mapper>