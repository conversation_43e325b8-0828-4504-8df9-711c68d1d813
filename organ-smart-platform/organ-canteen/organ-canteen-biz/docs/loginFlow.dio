<mxfile host="65bd71144e">
    <diagram id="m5ledx_1MU6uPFdWrmMz" name="Page-1">
        <mxGraphModel dx="1263" dy="1035" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1827" pageHeight="5069" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="16" style="edgeStyle=none;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" parent="1" source="8" target="11" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" style="edgeStyle=none;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" parent="1" source="8" target="12" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="760" y="950" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(251, 251, 251);&quot;&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;通过调用服务端获取用户信息接口&lt;/font&gt;&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(251, 251, 251);&quot;&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;（将AcceccToken作为参数）&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(251, 251, 251);&quot;&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;去找服务端拿登录token和对对应的用户信息&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];arcSize=12;" parent="23" vertex="1" connectable="0">
                    <mxGeometry x="-0.1667" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;用户端发起登录&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="440" width="200" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="20" style="edgeStyle=none;html=1;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" parent="1" source="11" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;&lt;font style=&quot;font-size: 18px;&quot;&gt;海政通授权登录&lt;/font&gt;&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="1320" y="440" width="200" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="26" style="edgeStyle=none;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" parent="1" source="12" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="32" style="edgeStyle=none;html=1;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" parent="1" source="12" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="400" y="1110"/>
                            <mxPoint x="400" y="560"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;将拿到的海政通用户信息&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;与智慧食堂内部的用户对应&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;返回海政通那边的登录token+智慧食堂内部的用户信息&lt;/font&gt;&lt;/div&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="32" vertex="1" connectable="0">
                    <mxGeometry x="0.0412" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="42" style="edgeStyle=none;html=1;exitX=0.98;exitY=0.618;exitDx=0;exitDy=0;entryX=-0.001;entryY=0.623;entryDx=0;entryDy=0;entryPerimeter=0;exitPerimeter=0;" edge="1" parent="1" source="12" target="13">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;智慧食堂服务端&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="600" y="960" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="28" style="edgeStyle=none;html=1;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=1;entryY=0.75;entryDx=0;entryDy=0;" parent="1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1320" y="1040" as="sourcePoint"/>
                        <mxPoint x="800" y="1040" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;海政通服务端&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="1320" y="960" width="200" height="200" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;通过AK并设置backUrl跳转第三方登录平台海政通&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="870" y="560" width="370" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;海政通登录后成功&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 18px;&quot;&gt;携带acccessToken跳转回backUrl&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="930" y="420" width="290" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;通过SK+accessToken去海政通服务端获取对应的token&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="865" y="968" width="410" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;SK校验通过后返回对应的token&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="955" y="1010" width="240" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="SysUserUtils" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="955" y="1360" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;通过SK+Token获取海政通对应的用户信息&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="900" y="1050" width="320" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="" style="endArrow=classic;html=1;exitX=0.004;exitY=0.809;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.995;entryY=0.795;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="13" target="12">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="890" y="950" as="sourcePoint"/>
                        <mxPoint x="940" y="900" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;返回海政通对应的用户信息&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="930" y="1090" width="220" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>